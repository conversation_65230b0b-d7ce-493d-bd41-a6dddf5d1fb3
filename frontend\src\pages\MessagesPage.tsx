import React, { useState, useRef, useEffect } from "react";
import { useLocation } from "react-router-dom";
import { useMessages } from "../hooks/useMessages";
import { useAuth } from "../contexts/AuthContext";
import LoadingSpinner from "../components/ui/LoadingSpinner";
import { messagesService } from "../services/messages";
import {
  PaperAirplaneIcon,
  PhotoIcon,
  FaceSmileIcon,
  InformationCircleIcon,
  PhoneIcon,
  VideoCameraIcon,
  PlusIcon,
  PencilSquareIcon,
} from "@heroicons/react/24/outline";
import { formatDistanceToNow } from "date-fns";
import { vi } from "date-fns/locale";
import { toast } from "react-hot-toast";
import type { User } from "../types";

const MessagesPage: React.FC = () => {
  const { user } = useAuth();
  const location = useLocation();
  const {
    conversations,
    currentConversation,
    messages,
    isLoading,
    onlineUsers,
    typingUsers,
    sendMessage,
    sendMediaMessage,
    startConversation,
    markAsRead,
    sendTyping,
    messagesEndRef,
    selectConversation,
  } = useMessages({});

  const [messageText, setMessageText] = useState("");
  const [showNewConversation, setShowNewConversation] = useState(false);
  const [searchUsers, setSearchUsers] = useState("");
  const [searchResults, setSearchResults] = useState<User[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    const handleStartWithUser = async (username: string) => {
      try {
        const conversation = await startConversation(username);
      } catch (error) {
        console.error("Error starting conversation with user:", error);
        toast.error("Không thể bắt đầu cuộc trò chuyện");
      }
    };

    const searchParams = new URLSearchParams(location.search);
    const targetUser = searchParams.get("user");

    if (targetUser) {
      handleStartWithUser(targetUser);
    }
  }, [location.search, startConversation]);

  useEffect(() => {
    if (currentConversation) {
      markAsRead(currentConversation.id);
    }
  }, [currentConversation, markAsRead]);

  const handleTyping = (text: string) => {
    setMessageText(text);
    if (currentConversation) {
      sendTyping(!!text, currentConversation.id);
    }
  };

  const handleSendMessage = () => {
    if (messageText.trim() && currentConversation) {
      sendMessage(messageText.trim(), currentConversation.id);
      setMessageText("");
    }
  };

  const handleFileUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file || !currentConversation) return;

    try {
      await sendMediaMessage(file, currentConversation.id);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    } catch (error) {
      console.error("Failed to send media:", error);
    }
  };

  const handleSearchUsers = async (query: string) => {
    setSearchUsers(query);
    if (query.trim()) {
      try {
        const response = await messagesService.searchUsers(query);
        setSearchResults(response.data || []);
      } catch (error) {
        setSearchResults([]);
      }
    } else {
      setSearchResults([]);
    }
  };

  const handleStartConversation = async (username: string) => {
    try {
      await startConversation(username);
      setShowNewConversation(false);
      setSearchUsers("");
      setSearchResults([]);
    } catch (error) {
      toast.error("Không thể tạo cuộc trò chuyện");
    }
  };

  const formatMessageTime = (date: string) => {
    return formatDistanceToNow(new Date(date), {
      addSuffix: true,
      locale: vi,
    });
  };

  const isUserOnline = (userId: number) => {
    return onlineUsers.has(userId);
  };

  const getTypingUsers = () => {
    if (!currentConversation || !typingUsers[currentConversation.id]) {
      return [];
    }
    return typingUsers[currentConversation.id] || [];
  };

  return (
    <div className="flex h-screen bg-white">
      {/* Sidebar */}
      <div className="w-1/3 border-r border-gray-200 flex flex-col">
        <div className="p-4 border-b border-gray-200 flex justify-between items-center">
          <h2 className="text-xl font-bold">{user?.username}</h2>
          <button onClick={() => setShowNewConversation(true)}>
            <PencilSquareIcon className="w-6 h-6" />
          </button>
        </div>
        <div className="flex-1 overflow-y-auto">
          {isLoading && conversations.length === 0 ? (
            <div className="p-4 text-center">Đang tải cuộc trò chuyện...</div>
          ) : (
            conversations.map((conversation) => {
              const otherUser = conversation.participants.find(
                (p) => p.id !== user?.id
              );
              const isOnline = otherUser ? isUserOnline(otherUser.id) : false;

              return (
                <div
                  key={conversation.id}
                  onClick={() => selectConversation(conversation.id)}
                  className={`flex items-center p-4 cursor-pointer hover:bg-gray-50 ${
                    currentConversation?.id === conversation.id
                      ? "bg-gray-100"
                      : ""
                  }`}
                >
                  <div className="relative">
                    <img
                      src={otherUser?.avatar || "/default-avatar.png"}
                      alt={otherUser?.username}
                      className="w-12 h-12 rounded-full object-cover"
                    />
                    {isOnline && (
                      <span className="absolute bottom-0 right-0 block h-3 w-3 rounded-full bg-green-500 border-2 border-white"></span>
                    )}
                  </div>
                  <div className="ml-4 flex-1">
                    <div className="font-semibold">{otherUser?.username}</div>
                    <div className="text-sm text-gray-500 truncate">
                      {conversation.last_message?.content}
                    </div>
                  </div>
                  <div className="text-xs text-gray-400">
                    {conversation.last_message &&
                      formatMessageTime(conversation.last_message.created_at)}
                  </div>
                </div>
              );
            })
          )}
        </div>
      </div>

      {/* Main Chat Area */}
      <div className="w-2/3 flex flex-col">
        {!currentConversation ? (
          <div className="flex-1 flex flex-col items-center justify-center">
            <PaperAirplaneIcon className="w-24 h-24 text-gray-300 transform -rotate-12" />
            <h3 className="mt-4 text-2xl font-light text-gray-800">
              Tin nhắn của bạn
            </h3>
            <p className="mt-2 text-gray-500">
              Gửi ảnh và tin nhắn riêng tư cho bạn bè.
            </p>
            <button
              onClick={() => setShowNewConversation(true)}
              className="mt-4 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              Gửi tin nhắn
            </button>
          </div>
        ) : (
          <>
            {/* Chat Header */}
            <div className="p-4 border-b border-gray-200 flex items-center justify-between">
              <div className="flex items-center">
                <img
                  src={
                    currentConversation.participants.find(
                      (p) => p.id !== user?.id
                    )?.avatar || "/default-avatar.png"
                  }
                  alt={
                    currentConversation.participants.find(
                      (p) => p.id !== user?.id
                    )?.username
                  }
                  className="w-10 h-10 rounded-full object-cover"
                />
                <div className="ml-4">
                  <div className="font-semibold">
                    {
                      currentConversation.participants.find(
                        (p) => p.id !== user?.id
                      )?.username
                    }
                  </div>
                  {isUserOnline(
                    currentConversation.participants.find(
                      (p) => p.id !== user?.id
                    )?.id || 0
                  ) && (
                    <div className="text-sm text-green-500">Đang hoạt động</div>
                  )}
                </div>
              </div>
              <div className="flex space-x-4">
                <button className="p-2 hover:bg-gray-100 rounded-full">
                  <PhoneIcon className="w-6 h-6" />
                </button>
                <button className="p-2 hover:bg-gray-100 rounded-full">
                  <VideoCameraIcon className="w-6 h-6" />
                </button>
                <button className="p-2 hover:bg-gray-100 rounded-full">
                  <InformationCircleIcon className="w-6 h-6" />
                </button>
              </div>
            </div>

            {/* Messages */}
            <div className="flex-1 p-6 overflow-y-auto">
              {isLoading ? (
                <div className="flex justify-center">
                  <LoadingSpinner size="lg" />
                </div>
              ) : messages && messages.length > 0 ? (
                messages.map((message) => {
                  // Convert both IDs to numbers for reliable comparison
                  const messageUserId = Number(message.user_id);
                  const currentUserId = Number(user?.id);
                  const isFromCurrentUser = messageUserId === currentUserId;

                  return (
                    <div
                      key={message.id}
                      className={`flex mb-4 ${
                        isFromCurrentUser
                          ? "justify-end"
                          : "justify-start"
                      }`}
                    >
                      <div
                        className={`max-w-md p-3 rounded-2xl ${
                          isFromCurrentUser
                            ? "bg-blue-500 text-white"
                            : "bg-gray-200 text-gray-900"
                        }`}
                      >
                        {message.type === 'image' && message.file_url ? (
                          <img
                            src={message.file_url}
                            alt="Shared image"
                            className="max-w-full h-auto rounded-lg"
                          />
                        ) : message.type === 'video' && message.file_url ? (
                          <video
                            src={message.file_url}
                            controls
                            className="max-w-full h-auto rounded-lg"
                          />
                        ) : (
                          message.content
                        )}
                        <div className="text-xs opacity-70 mt-1">
                          {new Date(message.created_at).toLocaleTimeString()}
                        </div>
                      </div>
                    </div>
                  );
                })
              ) : (
                <div className="text-center text-gray-500">
                  Chưa có tin nhắn nào. Bắt đầu cuộc trò chuyện!
                </div>
              )}
              <div ref={messagesEndRef} />
            </div>

            {/* Message Input */}
            <div className="p-4 border-t border-gray-200">
              {(() => {
                const typing = getTypingUsers();
                if (Array.isArray(typing) && typing.length > 0) {
                  return (
                    <div className="text-sm text-gray-500 mb-2 italic">
                      {typing.map((u: User) => u.username).join(", ")} đang
                      nhập...
                    </div>
                  );
                }
                return null;
              })()}
              <div className="relative">
                <input
                  type="text"
                  value={messageText}
                  onChange={(e) => handleTyping(e.target.value)}
                  onKeyDown={(e) => e.key === "Enter" && handleSendMessage()}
                  placeholder="Nhắn tin..."
                  className="w-full pl-12 pr-20 py-3 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <div className="absolute left-4 top-1/2 -translate-y-1/2">
                  <button className="p-1 hover:bg-gray-100 rounded-full">
                    <FaceSmileIcon className="w-6 h-6 text-gray-500" />
                  </button>
                </div>
                <div className="absolute right-4 top-1/2 -translate-y-1/2 flex space-x-2">
                  <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleFileUpload}
                    className="hidden"
                    accept="image/*,video/*"
                  />
                  <button
                    onClick={() => fileInputRef.current?.click()}
                    className="p-1 hover:bg-gray-100 rounded-full"
                  >
                    <PhotoIcon className="w-6 h-6 text-gray-500" />
                  </button>
                  <button
                    onClick={handleSendMessage}
                    disabled={!messageText.trim()}
                    className="p-1 text-blue-500 hover:text-blue-600 disabled:text-gray-300"
                  >
                    <PaperAirplaneIcon className="w-6 h-6" />
                  </button>
                </div>
              </div>
            </div>
          </>
        )}
      </div>

      {/* New Conversation Modal */}
      {showNewConversation && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
            <div className="p-4 border-b flex items-center justify-between">
              <h3 className="text-lg font-semibold">Tin nhắn mới</h3>
              <button
                onClick={() => setShowNewConversation(false)}
                className="text-gray-500 hover:text-gray-800"
              >
                &times;
              </button>
            </div>
            <div className="p-4">
              <input
                type="text"
                placeholder="Tìm kiếm người dùng..."
                value={searchUsers}
                onChange={(e) => handleSearchUsers(e.target.value)}
                className="w-full px-3 py-2 border rounded-lg"
              />
            </div>
            <div className="max-h-80 overflow-y-auto">
              {searchResults.length > 0 ? (
                searchResults.map((u: User) => (
                  <div
                    key={u.id}
                    onClick={() => handleStartConversation(u.username)}
                    className="flex items-center p-4 hover:bg-gray-50 cursor-pointer"
                  >
                    <img
                      src={u.avatar || "/default-avatar.png"}
                      alt={u.username}
                      className="w-10 h-10 rounded-full"
                    />
                    <div className="ml-3">{u.username}</div>
                  </div>
                ))
              ) : searchUsers ? (
                <div className="p-4 text-center text-gray-500">
                  Không tìm thấy người dùng.
                </div>
              ) : null}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MessagesPage;
