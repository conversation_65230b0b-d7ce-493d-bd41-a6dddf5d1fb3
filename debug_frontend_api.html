<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Frontend API</title>
</head>
<body>
    <h1>Debug Frontend API Calls</h1>
    <div>
        <button onclick="testLogin()">Test Login</button>
        <button onclick="testCurrentUser()">Test Current User</button>
        <button onclick="testMessages()">Test Messages</button>
    </div>
    <div id="output"></div>

    <script>
        const API_BASE = 'http://localhost:8001/api';
        let authToken = null;

        function log(message) {
            console.log(message);
            document.getElementById('output').innerHTML += message + '<br>';
        }

        async function apiCall(endpoint, method = 'GET', data = null) {
            const options = {
                method,
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                },
            };

            if (authToken) {
                options.headers['Authorization'] = `Bearer ${authToken}`;
            }

            if (data) {
                options.body = JSON.stringify(data);
            }

            try {
                const response = await fetch(`${API_BASE}${endpoint}`, options);
                const result = await response.json();
                
                log(`${method} ${endpoint}: ${response.status}`);
                return { response, result, error: null };
            } catch (error) {
                log(`❌ Error: ${error.message}`);
                return { response: null, result: null, error };
            }
        }

        async function testLogin() {
            log('=== TESTING LOGIN ===');
            
            const { response, result, error } = await apiCall('/login', 'POST', {
                login: 'duongnh2',
                password: 'password123'
            });
            
            if (error) return;
            
            if (result.success && result.data.token) {
                authToken = result.data.token;
                log(`✅ Login successful`);
                log(`Token: ${authToken.substring(0, 20)}...`);
                
                const user = result.data.user;
                log(`User ID: ${user.id} (type: ${typeof user.id})`);
                log(`Username: ${user.username}`);
                log(`Full Name: ${user.full_name}`);
                
                // Test localStorage simulation
                localStorage.setItem('debug_user', JSON.stringify(user));
                const retrievedUser = JSON.parse(localStorage.getItem('debug_user'));
                log(`Retrieved User ID: ${retrievedUser.id} (type: ${typeof retrievedUser.id})`);
            } else {
                log('❌ Login failed');
                log(JSON.stringify(result, null, 2));
            }
        }

        async function testCurrentUser() {
            log('\\n=== TESTING CURRENT USER ===');
            
            if (!authToken) {
                log('❌ Please login first');
                return;
            }
            
            const { response, result, error } = await apiCall('/user');
            
            if (error) return;
            
            if (result.success) {
                const user = result.data;
                log(`✅ Current user fetched`);
                log(`User ID: ${user.id} (type: ${typeof user.id})`);
                log(`Username: ${user.username}`);
                log(`Full Name: ${user.full_name}`);
            } else {
                log('❌ Failed to get current user');
                log(JSON.stringify(result, null, 2));
            }
        }

        async function testMessages() {
            log('\\n=== TESTING MESSAGES ===');
            
            if (!authToken) {
                log('❌ Please login first');
                return;
            }
            
            // Get conversations first
            const { response: convResponse, result: convResult, error: convError } = await apiCall('/conversations');
            
            if (convError) return;
            
            if (convResult.success && convResult.data.data.length > 0) {
                const conversation = convResult.data.data[0];
                log(`✅ Found conversation ${conversation.id}`);
                
                // Get messages for this conversation
                const { response: msgResponse, result: msgResult, error: msgError } = await apiCall(`/conversations/${conversation.id}/messages`);
                
                if (msgError) return;
                
                if (msgResult.success && msgResult.data.data.length > 0) {
                    log(`✅ Found ${msgResult.data.data.length} messages`);
                    
                    // Check first few messages
                    msgResult.data.data.slice(0, 3).forEach((msg, index) => {
                        log(`Message ${index + 1}:`);
                        log(`  ID: ${msg.id} (type: ${typeof msg.id})`);
                        log(`  User ID: ${msg.user_id} (type: ${typeof msg.user_id})`);
                        log(`  Content: "${msg.content}"`);
                        log(`  User: ${msg.user.username} (ID: ${msg.user.id}, type: ${typeof msg.user.id})`);
                        
                        // Test comparison with current user
                        const currentUser = JSON.parse(localStorage.getItem('debug_user'));
                        if (currentUser) {
                            log(`  Is from current user (===): ${msg.user_id === currentUser.id}`);
                            log(`  Is from current user (==): ${msg.user_id == currentUser.id}`);
                            log(`  Comparison details: ${msg.user_id} (${typeof msg.user_id}) vs ${currentUser.id} (${typeof currentUser.id})`);
                        }
                        log('');
                    });
                } else {
                    log('❌ No messages found');
                }
            } else {
                log('❌ No conversations found');
            }
        }
    </script>
</body>
</html>
