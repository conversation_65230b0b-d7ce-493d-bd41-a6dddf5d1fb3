{"name": "instagram-socket-server", "version": "1.0.0", "description": "Socket.IO server for Instagram clone realtime features", "main": "socket-server.js", "scripts": {"start": "node socket-server.js", "dev": "nodemon socket-server.js"}, "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.2", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["socket.io", "realtime", "instagram", "websocket"], "author": "", "license": "MIT"}