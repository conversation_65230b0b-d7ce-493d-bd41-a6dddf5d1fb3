<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Login Error</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>Test Login Error Response</h1>
    
    <div class="test-section">
        <h3>Test Invalid Login</h3>
        <p>This will test the API response when login credentials are invalid.</p>
        <button onclick="testInvalidLogin()">Test Invalid Login</button>
        <div id="invalidLoginResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>Test Valid Login (if you have credentials)</h3>
        <input type="text" id="validUsername" placeholder="Username/Email" style="margin: 5px; padding: 8px;">
        <input type="password" id="validPassword" placeholder="Password" style="margin: 5px; padding: 8px;">
        <button onclick="testValidLogin()">Test Valid Login</button>
        <div id="validLoginResult" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8001/api';

        async function testInvalidLogin() {
            const resultDiv = document.getElementById('invalidLoginResult');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                const response = await fetch(`${API_BASE}/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                    body: JSON.stringify({
                        login: '<EMAIL>',
                        password: 'wrong_password'
                    })
                });

                const data = await response.json();
                
                resultDiv.className = 'result ' + (response.ok ? 'success' : 'error');
                resultDiv.innerHTML = `Status: ${response.status}
Response: ${JSON.stringify(data, null, 2)}`;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `Error: ${error.message}`;
            }
        }

        async function testValidLogin() {
            const username = document.getElementById('validUsername').value;
            const password = document.getElementById('validPassword').value;
            const resultDiv = document.getElementById('validLoginResult');
            
            if (!username || !password) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = 'Please enter both username and password';
                return;
            }
            
            resultDiv.innerHTML = 'Testing...';
            
            try {
                const response = await fetch(`${API_BASE}/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                    body: JSON.stringify({
                        login: username,
                        password: password
                    })
                });

                const data = await response.json();
                
                resultDiv.className = 'result ' + (response.ok ? 'success' : 'error');
                resultDiv.innerHTML = `Status: ${response.status}
Response: ${JSON.stringify(data, null, 2)}`;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `Error: ${error.message}`;
            }
        }
    </script>
</body>
</html>
