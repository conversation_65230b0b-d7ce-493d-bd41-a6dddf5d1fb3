# Kị<PERSON> bản Test Chức năng Realtime - Instagram App

## Chu<PERSON><PERSON> bị

### 1. T<PERSON><PERSON> kho<PERSON>n test

Sử dụng các tài khoản đã tạo trong `user_accounts.csv`:

- **User 1**: `testuser` / password: `password`
- **User 2**: `g<PERSON><PERSON>` / password: `password`
- **User 3**: `mquigley` / password: `password`
- **User 4**: `bschimmel` / password: `password`

### 2. Môi trường

- Frontend: http://localhost:5173/
- Backend API: http://localhost:8000/
- Database: MySQL đã được seed với dữ liệu mẫu

## Test Cases

### Test Case 1: Realtime Likes

**Mục tiêu**: Kiểm tra việc cập nhật số lượng like realtime khi user khác like/unlike bài post

**Bước thực hiện**:

1. Mở 2 tab browser hoặc 2 browser khác nhau
2. Tab 1: <PERSON><PERSON><PERSON> nhập với `testuser`
3. Tab 2: <PERSON><PERSON><PERSON> nhập với `g<PERSON>hurst`
4. <PERSON><PERSON> 2 tab đề<PERSON> v<PERSON><PERSON> trang Home/Feed
5. Tab 1: Like một bài post
6. **Kiểm tra**: Tab 2 phải hiển thị số like tăng lên ngay lập tức (không cần refresh)
7. Tab 1: Unlike bài post đó
8. **Kiểm tra**: Tab 2 phải hiển thị số like giảm xuống ngay lập tức

**Kết quả mong đợi**: Số lượng like cập nhật realtime trên tất cả các client

### Test Case 2: Realtime Comments

**Mục tiêu**: Kiểm tra việc hiển thị comment mới realtime

**Bước thực hiện**:

1. Mở 2 tab browser
2. Tab 1: Đăng nhập với `testuser`
3. Tab 2: Đăng nhập với `mquigley`
4. Cả 2 tab đều mở cùng một bài post (click vào post để xem chi tiết)
5. Tab 1: Viết và gửi comment "Hello from testuser!"
6. **Kiểm tra**: Tab 2 phải hiển thị comment mới ngay lập tức
7. Tab 2: Reply comment "Hi back from mquigley!"
8. **Kiểm tra**: Tab 1 phải hiển thị reply ngay lập tức

**Kết quả mong đợi**: Comments và replies hiển thị realtime trên tất cả clients

### Test Case 3: Realtime Follow/Unfollow

**Mục tiêu**: Kiểm tra cập nhật trạng thái follow realtime

**Bước thực hiện**:

1. Mở 2 tab browser
2. Tab 1: Đăng nhập với `testuser`
3. Tab 2: Đăng nhập với `gmedhurst`
4. Tab 1: Vào profile của `gmedhurst`
5. Tab 2: Vào profile của chính mình (`gmedhurst`)
6. Tab 1: Click Follow `gmedhurst`
7. **Kiểm tra**: Tab 2 phải hiển thị số followers tăng lên ngay lập tức
8. Tab 1: Click Unfollow
9. **Kiểm tra**: Tab 2 phải hiển thị số followers giảm xuống ngay lập tức

**Kết quả mong đợi**: Số followers/following cập nhật realtime

### Test Case 4: Realtime Notifications

**Mục tiêu**: Kiểm tra thông báo realtime khi có hoạt động mới

**Bước thực hiện**:

1. Mở 2 tab browser
2. Tab 1: Đăng nhập với `testuser`
3. Tab 2: Đăng nhập với `bschimmel`
4. Tab 1: Like một bài post của `bschimmel`
5. **Kiểm tra**: Tab 2 phải hiển thị notification icon có số đếm mới
6. Tab 1: Comment vào bài post của `bschimmel`
7. **Kiểm tra**: Tab 2 phải hiển thị thêm notification
8. Tab 1: Follow `bschimmel`
9. **Kiểm tra**: Tab 2 phải hiển thị notification follow mới

**Kết quả mong đợi**: Notifications hiển thị realtime với số đếm chính xác

### Test Case 5: Realtime New Posts

**Mục tiêu**: Kiểm tra hiển thị bài post mới trong feed realtime

**Bước thực hiện**:

1. Mở 2 tab browser
2. Tab 1: Đăng nhập với `testuser`
3. Tab 2: Đăng nhập với `gmedhurst` (đã follow `testuser`)
4. Tab 2: Ở trang Home/Feed
5. Tab 1: Tạo bài post mới với hình ảnh và caption
6. **Kiểm tra**: Tab 2 phải hiển thị bài post mới ngay lập tức ở đầu feed

**Kết quả mong đợi**: Bài post mới xuất hiện realtime trong feed của followers

## Test Performance

### Test Case 6: Multiple Users Concurrent Actions

**Mục tiêu**: Kiểm tra hiệu suất khi nhiều user thực hiện hành động cùng lúc

**Bước thực hiện**:

1. Mở 4 tab browser với 4 tài khoản khác nhau
2. Tất cả đều vào cùng một bài post
3. Đồng thời:
   - Tab 1: Like post
   - Tab 2: Comment
   - Tab 3: Like post
   - Tab 4: Comment
4. **Kiểm tra**: Tất cả actions phải được cập nhật chính xác trên tất cả tabs

**Kết quả mong đợi**: Không có conflict, tất cả actions được xử lý đúng

## Debugging Tools

### 1. Browser Developer Tools

- Mở F12 → Network tab để xem WebSocket connections
- Console tab để xem errors
- Application tab để xem localStorage/sessionStorage

### 2. Backend Logs

```bash
# Xem Laravel logs
tail -f backend/storage/logs/laravel.log

# Xem realtime events (nếu có)
php artisan queue:work --verbose
```

### 3. Database Monitoring

```sql
-- Kiểm tra likes realtime
SELECT * FROM likes ORDER BY created_at DESC LIMIT 10;

-- Kiểm tra comments realtime
SELECT * FROM comments ORDER BY created_at DESC LIMIT 10;

-- Kiểm tra follows realtime
SELECT * FROM follows ORDER BY created_at DESC LIMIT 10;
```

## Checklist Kết quả

- [ ] Likes cập nhật realtime
- [ ] Comments hiển thị realtime
- [ ] Follow/Unfollow cập nhật realtime
- [ ] Notifications hoạt động realtime
- [ ] New posts xuất hiện realtime trong feed
- [ ] Performance tốt với multiple concurrent users
- [ ] Không có memory leaks trong browser
- [ ] WebSocket connections stable
- [ ] Error handling hoạt động đúng khi mất kết nối

## Lưu ý

1. **WebSocket Connection**: Kiểm tra xem ứng dụng có sử dụng WebSocket, Socket.IO, hoặc Server-Sent Events
2. **Fallback Mechanism**: Test khi mất kết nối internet tạm thời
3. **Browser Compatibility**: Test trên Chrome, Firefox, Safari
4. **Mobile Responsive**: Test trên mobile devices nếu có
5. **Rate Limiting**: Kiểm tra xem có rate limiting cho realtime actions không

## Troubleshooting

### Nếu realtime không hoạt động:

1. Kiểm tra WebSocket connection trong Network tab
2. Xem Laravel logs có errors không
3. Kiểm tra CORS settings
4. Verify authentication tokens
5. Check database connections
6. Restart both frontend và backend servers
