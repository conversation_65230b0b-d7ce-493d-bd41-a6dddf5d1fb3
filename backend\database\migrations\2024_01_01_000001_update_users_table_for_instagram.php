<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('username')->unique()->after('name');
            $table->string('phone')->nullable()->after('email');
            $table->text('bio')->nullable()->after('phone');
            $table->string('website')->nullable()->after('bio');
            $table->string('avatar')->nullable()->after('website');
            $table->string('cover_image')->nullable()->after('avatar');
            $table->boolean('is_private')->default(false)->after('cover_image');
            $table->timestamp('last_active_at')->nullable()->after('is_private');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'username',
                'phone',
                'bio',
                'website',
                'avatar',
                'cover_image',
                'is_private',
                'last_active_at'
            ]);
        });
    }
};