import { apiService } from './api';
import type {
  User,
  ProfileFormData,
  PaginatedResponse,
  ApiResponse,
  SearchResult,
} from '../types';

export class UsersService {
  // Get user profile
  async getUser(userId: number): Promise<User> {
    const response = await apiService.get<User>(`/users/${userId}`);
    return response.data;
  }

  // Get user by username
  async getUserByUsername(username: string): Promise<User> {
    const response = await apiService.get<User>(`/users/${username}`);
    return response.data;
  }

  // Update user profile
  async updateProfile(data: ProfileFormData): Promise<User> {
    const response = await apiService.put<User>('/profile', data);
    return response.data;
  }

  // Upload avatar
  async uploadAvatar(file: File, onProgress?: (progress: number) => void): Promise<User> {
    const formData = new FormData();
    formData.append('avatar', file);

    const response = await apiService.upload<User>('/profile/avatar', formData, onProgress);
    return response.data;
  }

  // Upload cover image
  async uploadCoverImage(file: File, onProgress?: (progress: number) => void): Promise<User> {
    const formData = new FormData();
    formData.append('cover_image', file);

    const response = await apiService.upload<User>('/profile/cover', formData, onProgress);
    return response.data;
  }

  // Follow/Unfollow user
  async toggleFollow(userId: number): Promise<{ is_following: boolean; followers_count: number }> {
    const response = await apiService.post<{ is_following: boolean; followers_count: number }>(`/users/${userId}/follow`);
    return response.data;
  }

  // Follow/Unfollow user by username
  async toggleFollowByUsername(username: string): Promise<{ is_following: boolean; followers_count: number; status?: string }> {
    // First check current follow status
    const statusResponse = await apiService.get<{ data: { follow_status: string | null } }>(`/users/${username}/follow-status`);
    const currentStatus = statusResponse.data.follow_status;

    if (currentStatus === 'accepted' || currentStatus === 'pending') {
      // Unfollow/Cancel request
      const response = await apiService.delete<{ is_following: boolean; followers_count: number; follow_status?: string }>(`/users/${username}/follow`);
      return {
        is_following: response.is_following || false,
        followers_count: response.followers_count || 0,
        status: response.follow_status || null
      };
    } else {
      // Follow (will create a follow request)
      const response = await apiService.post<{ is_following: boolean; followers_count: number; status?: string }>(`/users/${username}/follow`);
      return {
        is_following: response.is_following || false,
        followers_count: response.followers_count || 0,
        status: response.status || 'pending'
      };
    }
  }

  // Get follow request ID from follower ID
  async getFollowRequestId(followerId: number): Promise<{ success: boolean; follow_request_id?: number }> {
    const response = await apiService.get<{ success: boolean; follow_request_id?: number }>(`/follow-requests/from/${followerId}`);
    return response;
  }

  // Accept follow request
  async acceptFollowRequest(followRequestId: number): Promise<{ success: boolean }> {
    const response = await apiService.post<{ success: boolean }>(`/follow-requests/${followRequestId}/accept`);
    return response.data;
  }

  // Reject follow request
  async rejectFollowRequest(followRequestId: number): Promise<{ success: boolean }> {
    const response = await apiService.post<{ success: boolean }>(`/follow-requests/${followRequestId}/reject`);
    return response.data;
  }

  // Get pending follow requests
  async getPendingFollowRequests(page: number = 1): Promise<PaginatedResponse<User>> {
    return await apiService.getPaginated<User>('/users/follow-requests', { page });
  }

  // Get user followers
  async getUserFollowers(userId: number, page: number = 1): Promise<PaginatedResponse<User>> {
    return await apiService.getPaginated<User>(`/users/${userId}/followers`, { page });
  }

  // Get user following
  async getUserFollowing(userId: number, page: number = 1): Promise<PaginatedResponse<User>> {
    return await apiService.getPaginated<User>(`/users/${userId}/following`, { page });
  }

  // Search users
  async searchUsers(query: string, page: number = 1): Promise<PaginatedResponse<User>> {
    return await apiService.getPaginated<User>('/users/search', { q: query, page });
  }

  // Get suggested users
  async getSuggestedUsers(limit: number = 5): Promise<User[]> {
    const response = await apiService.get<User[]>('/users/suggestions', { limit });
    return response.data;
  }

  // Global search
  async globalSearch(query: string): Promise<SearchResult> {
    const response = await apiService.get<SearchResult>('/search', { q: query });
    return response.data;
  }

  // Block/Unblock user
  async toggleBlock(userId: number): Promise<{ is_blocked: boolean }> {
    const response = await apiService.post<{ is_blocked: boolean }>(`/users/${userId}/block`);
    return response.data;
  }

  // Get blocked users
  async getBlockedUsers(page: number = 1): Promise<PaginatedResponse<User>> {
    return await apiService.getPaginated<User>('/users/blocked', { page });
  }

  // Report user
  async reportUser(userId: number, reason: string): Promise<ApiResponse<{ success: boolean }>> {
    return await apiService.post(`/users/${userId}/report`, { reason });
  }

  // Check if username is available
  async checkUsernameAvailability(username: string): Promise<{ available: boolean }> {
    const response = await apiService.get<{ available: boolean }>('/users/check-username', { username });
    return response.data;
  }

  // Get user activity (likes, comments, etc.)
  async getUserActivity(userId: number, page: number = 1): Promise<PaginatedResponse<{ type: string; data: unknown; timestamp: string }>> {
    return await apiService.getPaginated(`/users/${userId}/activity`, { page });
  }

  // Update privacy settings
  async updatePrivacySettings(settings: {
    is_private?: boolean;
    show_activity_status?: boolean;
    allow_message_requests?: boolean;
  }): Promise<User> {
    const response = await apiService.put<User>('/users/privacy', settings);
    return response.data;
  }

  // Get mutual followers
  async getMutualFollowers(userId: number, page: number = 1): Promise<PaginatedResponse<User>> {
    return await apiService.getPaginated<User>(`/users/${userId}/mutual-followers`, { page });
  }

  // Get user statistics
  async getUserStats(userId: number): Promise<{
    posts_count: number;
    followers_count: number;
    following_count: number;
    likes_received: number;
    comments_received: number;
  }> {
    const response = await apiService.get<{ posts_count: number; followers_count: number; following_count: number }>(`/users/${userId}/stats`);
    return response.data;
  }

  // Delete account
  async deleteAccount(password: string): Promise<ApiResponse<{ success: boolean }>> {
    return await apiService.post('/users/delete-account', { password });
  }

  // Export user data
  async exportUserData(): Promise<ApiResponse<{ download_url: string }>> {
    return await apiService.post('/users/export-data');
  }
}

export const usersService = new UsersService();
export default usersService;