import React, { useState, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";
import { usePosts } from "../hooks/usePosts";
import LoadingSpinner from "../components/ui/LoadingSpinner";
import {
  PhotoIcon,
  XMarkIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  MapPinIcon,
  UserGroupIcon,
} from "@heroicons/react/24/outline";
import { toast } from "react-hot-toast";

interface MediaFile {
  id: string;
  file: File;
  url: string;
  type: "image" | "video";
}

const CreatePostPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { createPost } = usePosts();
  const [step, setStep] = useState<"select" | "edit" | "share">("select");
  const [mediaFiles, setMediaFiles] = useState<MediaFile[]>([]);
  const [currentMediaIndex, setCurrentMediaIndex] = useState(0);
  const [caption, setCaption] = useState("");
  const [location, setLocation] = useState("");
  const [taggedUsers, setTaggedUsers] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [showLocationSearch, setShowLocationSearch] = useState(false);
  const [showUserSearch, setShowUserSearch] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const fileInputRef = useRef<HTMLInputElement>(null);
  console.log("user", user);
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);

    if (files.length === 0) return;

    const newMediaFiles: MediaFile[] = files.map((file, index) => ({
      id: `${Date.now()}-${index}`,
      file,
      url: URL.createObjectURL(file),
      type: file.type.startsWith("image/") ? "image" : "video",
    }));

    setMediaFiles(newMediaFiles);
    setStep("edit");
  };

  const removeMedia = (id: string) => {
    setMediaFiles((prev) => {
      const filtered = prev.filter((media) => media.id !== id);
      if (filtered.length === 0) {
        setStep("select");
        return [];
      }

      // Adjust current index if needed
      if (currentMediaIndex >= filtered.length) {
        setCurrentMediaIndex(filtered.length - 1);
      }

      return filtered;
    });
  };

  const handleNext = () => {
    if (step === "edit") {
      setStep("share");
    }
  };

  const handleBack = () => {
    if (step === "share") {
      setStep("edit");
    } else if (step === "edit") {
      setStep("select");
      setMediaFiles([]);
      setCurrentMediaIndex(0);
    }
  };

  const handleShare = async () => {
    if (mediaFiles.length === 0) {
      toast.error("Vui lòng chọn ít nhất một ảnh hoặc video");
      return;
    }

    try {
      setLoading(true);

      // Prepare form data for API
      const postData = {
        caption: caption.trim() || undefined,
        location: location.trim() || undefined,
        media: mediaFiles.map((media) => media.file), // Extract File objects
      };

      // Call real API to create post
      await createPost(postData, (progress) => {
        // Optional: You can show upload progress here
        console.log(`Upload progress: ${progress}%`);
      });

      // Reset form
      setMediaFiles([]);
      setCaption("");
      setLocation("");
      setTaggedUsers([]);
      setStep("select");
      setCurrentMediaIndex(0);

      // Navigate to home page
      navigate("/");
    } catch (error) {
      console.error("Error creating post:", error);
      // Error message is already handled by usePosts hook
    } finally {
      setLoading(false);
    }
  };

  const mockLocations = [
    "Hà Nội, Việt Nam",
    "Thành phố Hồ Chí Minh, Việt Nam",
    "Đà Nẵng, Việt Nam",
    "Hội An, Quảng Nam",
    "Sa Pa, Lào Cai",
  ];

  const mockUsers = [
    { id: "1", username: "john_doe", full_name: "John Doe" },
    { id: "2", username: "jane_smith", full_name: "Jane Smith" },
    { id: "3", username: "photographer_pro", full_name: "Pro Photographer" },
  ];

  const filteredLocations = mockLocations.filter((loc) =>
    loc.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const filteredUsers = mockUsers.filter(
    (user) =>
      user.username.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.full_name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="flex items-center justify-between max-w-4xl mx-auto">
          <div className="flex items-center space-x-4">
            {step !== "select" && (
              <button
                onClick={handleBack}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors"
              >
                <ChevronLeftIcon className="w-6 h-6" />
              </button>
            )}
            <h1 className="text-xl font-semibold">
              {step === "select" && "Tạo bài viết mới"}
              {step === "edit" && "Chỉnh sửa"}
              {step === "share" && "Chia sẻ"}
            </h1>
          </div>

          {step === "edit" && (
            <button
              onClick={handleNext}
              className="text-blue-500 font-semibold hover:text-blue-600 transition-colors"
            >
              Tiếp theo
            </button>
          )}

          {step === "share" && (
            <button
              onClick={handleShare}
              disabled={loading}
              className="text-blue-500 font-semibold hover:text-blue-600 transition-colors disabled:opacity-50 flex items-center space-x-2"
            >
              {loading ? (
                <>
                  <LoadingSpinner size="sm" />
                  <span>Đang đăng...</span>
                </>
              ) : (
                <span>Chia sẻ</span>
              )}
            </button>
          )}
        </div>
      </div>

      <div className="max-w-4xl mx-auto">
        {/* Select Media Step */}
        {step === "select" && (
          <div className="bg-white rounded-lg shadow-sm m-4">
            <div className="p-8 text-center">
              <PhotoIcon className="w-24 h-24 text-gray-400 mx-auto mb-4" />
              <h2 className="text-2xl font-light mb-2">
                Kéo ảnh và video vào đây
              </h2>
              <p className="text-gray-600 mb-6">Chọn tối đa 10 tệp</p>

              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileSelect}
                accept="image/*,video/*"
                multiple
                className="hidden"
              />

              <button
                onClick={() => fileInputRef.current?.click()}
                className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              >
                Chọn từ máy tính
              </button>
            </div>
          </div>
        )}

        {/* Edit Media Step */}
        {step === "edit" && mediaFiles.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm m-4">
            <div className="aspect-square relative bg-black rounded-t-lg overflow-hidden">
              {/* Media Display */}
              <div className="relative w-full h-full flex items-center justify-center">
                {mediaFiles[currentMediaIndex].type === "image" ? (
                  <img
                    src={mediaFiles[currentMediaIndex].url}
                    alt="Preview"
                    className="max-w-full max-h-full object-contain"
                  />
                ) : (
                  <video
                    src={mediaFiles[currentMediaIndex].url}
                    controls
                    className="max-w-full max-h-full object-contain"
                  />
                )}

                {/* Navigation */}
                {mediaFiles.length > 1 && (
                  <>
                    {currentMediaIndex > 0 && (
                      <button
                        onClick={() => setCurrentMediaIndex((prev) => prev - 1)}
                        className="absolute left-4 top-1/2 transform -translate-y-1/2 p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-75 transition-all"
                      >
                        <ChevronLeftIcon className="w-6 h-6" />
                      </button>
                    )}

                    {currentMediaIndex < mediaFiles.length - 1 && (
                      <button
                        onClick={() => setCurrentMediaIndex((prev) => prev + 1)}
                        className="absolute right-4 top-1/2 transform -translate-y-1/2 p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-75 transition-all"
                      >
                        <ChevronRightIcon className="w-6 h-6" />
                      </button>
                    )}
                  </>
                )}

                {/* Remove Button */}
                <button
                  onClick={() => removeMedia(mediaFiles[currentMediaIndex].id)}
                  className="absolute top-4 right-4 p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-75 transition-all"
                >
                  <XMarkIcon className="w-6 h-6" />
                </button>
              </div>

              {/* Media Indicators */}
              {mediaFiles.length > 1 && (
                <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                  {mediaFiles.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentMediaIndex(index)}
                      className={`w-2 h-2 rounded-full transition-all ${
                        index === currentMediaIndex
                          ? "bg-white"
                          : "bg-white bg-opacity-50"
                      }`}
                    />
                  ))}
                </div>
              )}
            </div>

            {/* Media Thumbnails */}
            {mediaFiles.length > 1 && (
              <div className="p-4 border-t border-gray-200">
                <div className="flex space-x-2 overflow-x-auto">
                  {mediaFiles.map((media, index) => (
                    <button
                      key={media.id}
                      onClick={() => setCurrentMediaIndex(index)}
                      className={`flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all ${
                        index === currentMediaIndex
                          ? "border-blue-500"
                          : "border-gray-200"
                      }`}
                    >
                      {media.type === "image" ? (
                        <img
                          src={media.url}
                          alt="Thumbnail"
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <video
                          src={media.url}
                          className="w-full h-full object-cover"
                        />
                      )}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Share Step */}
        {step === "share" && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 m-4">
            {/* Media Preview */}
            <div className="bg-white rounded-lg shadow-sm">
              <div className="aspect-square relative bg-black rounded-lg overflow-hidden">
                {mediaFiles[currentMediaIndex].type === "image" ? (
                  <img
                    src={mediaFiles[currentMediaIndex].url}
                    alt="Preview"
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <video
                    src={mediaFiles[currentMediaIndex].url}
                    controls
                    className="w-full h-full object-cover"
                  />
                )}

                {mediaFiles.length > 1 && (
                  <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                    {mediaFiles.map((_, index) => (
                      <div
                        key={index}
                        className={`w-2 h-2 rounded-full ${
                          index === currentMediaIndex
                            ? "bg-white"
                            : "bg-white bg-opacity-50"
                        }`}
                      />
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Post Details */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              {/* User Info */}
              <div className="flex items-center mb-6">
                <img
                  src={
                    user?.user?.avatar ||
                    user?.avatar ||
                    "https://api-private.atlassian.com/users/8b3597e8a7d1d8f2ed7f58bcab1946b8/avatar"
                  }
                  alt={user?.user?.username || user?.username}
                  className="w-8 h-8 rounded-full object-cover mr-3"
                />
                <span className="font-semibold">
                  {user?.user?.username || user?.username}
                </span>
              </div>

              {/* Caption */}
              <div className="mb-6">
                <textarea
                  value={caption}
                  onChange={(e) => setCaption(e.target.value)}
                  placeholder="Viết chú thích..."
                  className="w-full h-32 p-3 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  maxLength={2200}
                />
                <div className="text-right text-sm text-gray-500 mt-1">
                  {caption.length}/2,200
                </div>
              </div>

              {/* Location */}
              <div className="mb-6">
                <div className="relative">
                  <button
                    onClick={() => setShowLocationSearch(!showLocationSearch)}
                    className="flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors"
                  >
                    <MapPinIcon className="w-5 h-5" />
                    <span>{location || "Thêm vị trí"}</span>
                  </button>

                  {showLocationSearch && (
                    <div className="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg mt-1 z-10">
                      <input
                        type="text"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        placeholder="Tìm kiếm vị trí..."
                        className="w-full p-3 border-b border-gray-200 focus:outline-none"
                        autoFocus
                      />
                      <div className="max-h-48 overflow-y-auto">
                        {filteredLocations.map((loc, index) => (
                          <button
                            key={index}
                            onClick={() => {
                              setLocation(loc);
                              setShowLocationSearch(false);
                              setSearchQuery("");
                            }}
                            className="w-full text-left p-3 hover:bg-gray-50 transition-colors"
                          >
                            {loc}
                          </button>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Tag Users */}
              <div className="mb-6">
                <div className="relative">
                  <button
                    onClick={() => setShowUserSearch(!showUserSearch)}
                    className="flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors"
                  >
                    <UserGroupIcon className="w-5 h-5" />
                    <span>
                      {taggedUsers.length > 0
                        ? `Đã gắn thẻ ${taggedUsers.length} người`
                        : "Gắn thẻ mọi người"}
                    </span>
                  </button>

                  {showUserSearch && (
                    <div className="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg mt-1 z-10">
                      <input
                        type="text"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        placeholder="Tìm kiếm người dùng..."
                        className="w-full p-3 border-b border-gray-200 focus:outline-none"
                        autoFocus
                      />
                      <div className="max-h-48 overflow-y-auto">
                        {filteredUsers.map((user) => (
                          <button
                            key={user.id}
                            onClick={() => {
                              if (!taggedUsers.includes(user.username)) {
                                setTaggedUsers((prev) => [
                                  ...prev,
                                  user.username,
                                ]);
                              }
                              setShowUserSearch(false);
                              setSearchQuery("");
                            }}
                            className="w-full text-left p-3 hover:bg-gray-50 transition-colors flex items-center space-x-3"
                          >
                            <div className="w-8 h-8 bg-gray-200 rounded-full" />
                            <div>
                              <div className="font-semibold text-sm">
                                {user.username}
                              </div>
                              <div className="text-gray-500 text-xs">
                                {user.full_name}
                              </div>
                            </div>
                          </button>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* Tagged Users */}
                {taggedUsers.length > 0 && (
                  <div className="mt-3 flex flex-wrap gap-2">
                    {taggedUsers.map((username) => (
                      <span
                        key={username}
                        className="inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
                      >
                        @{username}
                        <button
                          onClick={() =>
                            setTaggedUsers((prev) =>
                              prev.filter((u) => u !== username)
                            )
                          }
                          className="ml-2 text-blue-600 hover:text-blue-800"
                        >
                          <XMarkIcon className="w-4 h-4" />
                        </button>
                      </span>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CreatePostPage;
