# Instagram Clone API Documentation

This is a Laravel-based REST API for an Instagram clone application.

## Features

-   User authentication (register, login, logout)
-   User profiles with avatar and cover images
-   Post creation with multiple media support
-   Like and comment system
-   Follow/unfollow functionality
-   Direct messaging
-   Hashtag system
-   Search functionality
-   Privacy settings

## Installation

1. Install dependencies:

```bash
composer install
```

2. Copy environment file:

```bash
cp .env.example .env
```

3. Generate application key:

```bash
php artisan key:generate
```

4. Configure your database in `.env` file

5. Run migrations:

```bash
php artisan migrate
```

6. Create storage link:

```bash
php artisan storage:link
```

7. Start the development server:

```bash
php artisan serve
```

## API Endpoints

### Authentication

-   `POST /api/register` - Register a new user
-   `POST /api/login` - Login user
-   `POST /api/logout` - Logout user (requires auth)
-   `POST /api/logout-all` - Logout from all devices (requires auth)
-   `GET /api/user` - Get authenticated user details (requires auth)
-   `POST /api/change-password` - Change password (requires auth)
-   `POST /api/forgot-password` - Send password reset email
-   `POST /api/reset-password` - Reset password with token

### User Management

-   `GET /api/users` - List users with search
-   `GET /api/users/{username}` - Get user profile
-   `GET /api/users/{username}/posts` - Get user's posts
-   `GET /api/users/{username}/followers` - Get user's followers (requires auth)
-   `GET /api/users/{username}/following` - Get user's following (requires auth)
-   `PUT /api/profile` - Update profile (requires auth)
-   `POST /api/profile/avatar` - Update avatar (requires auth)
-   `DELETE /api/profile/avatar` - Remove avatar (requires auth)
-   `POST /api/profile/cover` - Update cover image (requires auth)
-   `DELETE /api/profile/cover` - Remove cover image (requires auth)

### Posts

-   `GET /api/posts` - Get feed posts (requires auth)
-   `POST /api/posts` - Create new post (requires auth)
-   `GET /api/posts/{id}` - Get single post
-   `PUT /api/posts/{id}` - Update post (requires auth)
-   `DELETE /api/posts/{id}` - Delete post (requires auth)
-   `POST /api/posts/{id}/like` - Like post (requires auth)
-   `DELETE /api/posts/{id}/like` - Unlike post (requires auth)
-   `GET /api/posts/explore` - Explore random posts
-   `GET /api/posts/search` - Search posts

### Comments

-   `GET /api/posts/{postId}/comments` - Get post comments (requires auth)
-   `POST /api/posts/{postId}/comments` - Add comment (requires auth)
-   `GET /api/comments/{id}` - Get single comment (requires auth)
-   `PUT /api/comments/{id}` - Update comment (requires auth)
-   `DELETE /api/comments/{id}` - Delete comment (requires auth)
-   `POST /api/comments/{id}/like` - Like comment (requires auth)
-   `DELETE /api/comments/{id}/like` - Unlike comment (requires auth)
-   `GET /api/comments/{id}/replies` - Get comment replies (requires auth)

### Follow System

-   `POST /api/users/{username}/follow` - Follow user (requires auth)
-   `DELETE /api/users/{username}/follow` - Unfollow user (requires auth)
-   `POST /api/follow-requests/{id}/accept` - Accept follow request (requires auth)
-   `POST /api/follow-requests/{id}/reject` - Reject follow request (requires auth)
-   `GET /api/follow-requests/pending` - Get pending requests (requires auth)
-   `GET /api/follow-requests/sent` - Get sent requests (requires auth)
-   `DELETE /api/follow-requests/{id}/cancel` - Cancel request (requires auth)
-   `DELETE /api/followers/{id}/remove` - Remove follower (requires auth)
-   `GET /api/users/{username}/follow-status` - Check follow status (requires auth)

### Messages

-   `GET /api/conversations` - Get conversations (requires auth)
-   `GET /api/conversations/{username}/direct` - Get/create direct conversation (requires auth)
-   `GET /api/conversations/{conversationId}/messages` - Get messages (requires auth)
-   `POST /api/conversations/{conversationId}/messages` - Send message (requires auth)
-   `PUT /api/messages/{messageId}` - Edit message (requires auth)
-   `DELETE /api/messages/{messageId}` - Delete message (requires auth)
-   `POST /api/conversations/{conversationId}/read` - Mark as read (requires auth)
-   `GET /api/users/search` - Search users for messaging (requires auth)

### Hashtags

-   `GET /api/hashtags` - Get popular hashtags
-   `GET /api/hashtags/trending` - Get trending hashtags
-   `GET /api/hashtags/search` - Search hashtags
-   `GET /api/hashtags/{slug}` - Get hashtag posts
-   `GET /api/hashtags/{slug}/related` - Get related hashtags

## Authentication

The API uses Laravel Sanctum for authentication. Include the bearer token in the Authorization header:

```
Authorization: Bearer {your-token}
```

## File Uploads

File uploads are supported for:

-   User avatars and cover images
-   Post media (images and videos)
-   Message attachments

Files are stored in the `storage/app/public` directory and accessible via the `/storage` URL.

## Response Format

All API responses follow this format:

```json
{
  "success": true|false,
  "message": "Response message",
  "data": {}, // Response data
  "errors": {} // Validation errors (if any)
}
```

## Error Handling

-   `400` - Bad Request
-   `401` - Unauthorized
-   `403` - Forbidden
-   `404` - Not Found
-   `422` - Validation Error
-   `500` - Internal Server Error

## Database Schema

The application uses the following main tables:

-   `users` - User accounts
-   `posts` - User posts
-   `post_media` - Post media files
-   `comments` - Post comments
-   `likes` - Likes for posts and comments
-   `follows` - Follow relationships
-   `conversations` - Direct message conversations
-   `conversation_participants` - Conversation participants
-   `messages` - Direct messages
-   `hashtags` - Hashtag definitions
-   `post_hashtags` - Post-hashtag relationships

## Development

For development, you can use:

```bash
# Run tests
php artisan test

# Clear cache
php artisan cache:clear
php artisan config:clear
php artisan route:clear

# Generate API documentation
php artisan route:list
```
