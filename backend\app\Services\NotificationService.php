<?php

namespace App\Services;

use App\Models\Notification;
use App\Models\User;
use App\Models\Post;
use App\Models\Comment;

class NotificationService
{
    /**
     * Create a like notification
     */
    public static function createLikeNotification(User $actor, Post $post)
    {
        // Don't notify if user likes their own post
        if ($actor->id === $post->user_id) {
            return;
        }

        Notification::create([
            'user_id' => $post->user_id,
            'type' => 'like',
            'data' => [
                'actor_id' => $actor->id,
                'post_id' => $post->id,
            ],
        ]);
    }

    /**
     * Create a comment notification
     */
    public static function createCommentNotification(User $actor, Comment $comment, Post $post)
    {
        // Don't notify if user comments on their own post
        if ($actor->id === $post->user_id) {
            return;
        }

        Notification::create([
            'user_id' => $post->user_id,
            'type' => 'comment',
            'data' => [
                'actor_id' => $actor->id,
                'post_id' => $post->id,
                'comment_id' => $comment->id,
            ],
        ]);
    }

    /**
     * Create a follow notification
     */
    public static function createFollowNotification(User $follower, User $following)
    {
        Notification::create([
            'user_id' => $following->id,
            'type' => 'follow',
            'data' => [
                'actor_id' => $follower->id,
            ],
        ]);
    }

    /**
     * Create a follow request notification
     */
    public static function createFollowRequestNotification(User $follower, User $following)
    {
        Notification::create([
            'user_id' => $following->id,
            'type' => 'follow_request',
            'data' => [
                'actor_id' => $follower->id,
            ],
        ]);
    }

    /**
     * Create a mention notification
     */
    public static function createMentionNotification(User $actor, User $mentionedUser, Post $post, Comment $comment = null)
    {
        // Don't notify if user mentions themselves
        if ($actor->id === $mentionedUser->id) {
            return;
        }

        $data = [
            'actor_id' => $actor->id,
            'post_id' => $post->id,
        ];

        if ($comment) {
            $data['comment_id'] = $comment->id;
        }

        Notification::create([
            'user_id' => $mentionedUser->id,
            'type' => 'mention',
            'data' => $data,
        ]);
    }

    /**
     * Remove like notification
     */
    public static function removeLikeNotification(User $actor, Post $post)
    {
        Notification::where('user_id', $post->user_id)
            ->where('type', 'like')
            ->whereJsonContains('data->actor_id', $actor->id)
            ->whereJsonContains('data->post_id', $post->id)
            ->delete();
    }

    /**
     * Remove follow notification
     */
    public static function removeFollowNotification(User $follower, User $following)
    {
        Notification::where('user_id', $following->id)
            ->where('type', 'follow')
            ->whereJsonContains('data->actor_id', $follower->id)
            ->delete();
    }

    /**
     * Remove follow request notification
     */
    public static function removeFollowRequestNotification(User $follower, User $following)
    {
        Notification::where('user_id', $following->id)
            ->where('type', 'follow_request')
            ->whereJsonContains('data->actor_id', $follower->id)
            ->delete();
    }
}