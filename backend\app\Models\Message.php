<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Storage;

class Message extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'conversation_id',
        'user_id',
        'type',
        'content',
        'file_path',
        'metadata',
        'edited_at',
    ];

    protected $casts = [
        'metadata' => 'array',
        'edited_at' => 'datetime',
    ];

    protected $appends = [
        'file_url',
        'sender_id',
        'sender',
    ];

    // Relationships
    public function conversation()
    {
        return $this->belongsTo(Conversation::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // Accessors
    public function getFileUrlAttribute()
    {
        return $this->file_path ? Storage::url($this->file_path) : null;
    }

    public function getSenderIdAttribute()
    {
        return $this->user_id;
    }

    public function getSenderAttribute()
    {
        return $this->user;
    }

    public function getIsTextAttribute()
    {
        return $this->type === 'text';
    }

    public function getIsImageAttribute()
    {
        return $this->type === 'image';
    }

    public function getIsVideoAttribute()
    {
        return $this->type === 'video';
    }

    public function getIsPostShareAttribute()
    {
        return $this->type === 'post_share';
    }

    public function getIsEditedAttribute()
    {
        return !is_null($this->edited_at);
    }

    // Helper methods
    public function edit($newContent)
    {
        if ($this->type !== 'text') {
            return false;
        }

        $this->update([
            'content' => $newContent,
            'edited_at' => now(),
        ]);

        return true;
    }

    public function getSharedPost()
    {
        if ($this->type !== 'post_share' || !isset($this->metadata['post_id'])) {
            return null;
        }

        return Post::find($this->metadata['post_id']);
    }

    protected static function boot()
    {
        parent::boot();

        static::created(function ($message) {
            // Update conversation's last_message_at
            $message->conversation->update([
                'last_message_at' => $message->created_at,
            ]);
        });

        static::deleting(function ($message) {
            // Delete file if exists
            if ($message->file_path && Storage::exists($message->file_path)) {
                Storage::delete($message->file_path);
            }
        });
    }
}
