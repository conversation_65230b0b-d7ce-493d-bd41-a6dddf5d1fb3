import { apiService } from './api';
import type {
  Conversation,
  Message,
  PaginatedResponse,
  ApiResponse,
  User,
} from '../types';

export class MessagesService {
  // Get user conversations
  async getConversations(page: number = 1): Promise<PaginatedResponse<Conversation>> {
    const response = await apiService.getPaginated<Conversation>('/conversations', { page });
    console.log('getConversations API response:', response);
    return response;
  }

  // Get conversation by ID
  async getConversationById(conversationId: number): Promise<Conversation> {
    const response = await apiService.get<Conversation>(`/conversations/${conversationId}`);
    return response.data;
  }

  // Get or create conversation
  async getOrCreateConversation(username: string): Promise<Conversation> {
    const response = await apiService.get<Conversation>(`/conversations/${username}/direct`);
    return response.data;
  }

  // Get conversation messages
  async getConversationMessages(
    conversationId: number,
    page: number = 1,
    limit: number = 50
  ): Promise<PaginatedResponse<Message>> {
    const response = await apiService.getPaginated<Message>(
      `/conversations/${conversationId}/messages`,
      { page, limit }
    );
    console.log(`getConversationMessages(${conversationId}) API response:`, response);
    return response;
  }

  // Send text message
  async sendMessage(conversationId: number, content: string): Promise<Message> {
    const response = await apiService.post<Message>(
      `/conversations/${conversationId}/messages`,
      { content }
    );
    return response.data;
  }

  // Send media message
  async sendMediaMessage(
    conversationId: number,
    file: File,
    onProgress?: (progress: number) => void
  ): Promise<Message> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', file.type.startsWith('image/') ? 'image' : 'video');

    const response = await apiService.upload<Message>(
      `/conversations/${conversationId}/messages`,
      formData,
      onProgress
    );
    return response.data;
  }

  // Mark messages as read
  async markAsRead(conversationId: number): Promise<ApiResponse<{ success: boolean }>> {
    return await apiService.post(`/conversations/${conversationId}/read`);
  }

  // Delete message
  async deleteMessage(messageId: number): Promise<void> {
    await apiService.delete(`/messages/${messageId}`);
  }

  // Delete conversation
  async deleteConversation(conversationId: number): Promise<void> {
    await apiService.delete(`/conversations/${conversationId}`);
  }

  // Search conversations
  async searchConversations(query: string): Promise<Conversation[]> {
    const response = await apiService.get<Conversation[]>('/conversations/search', { q: query });
    return response.data;
  }

  // Search messages in conversation
  async searchMessages(conversationId: number, query: string): Promise<Message[]> {
    const response = await apiService.get<Message[]>(
      `/conversations/${conversationId}/search`,
      { q: query }
    );
    return response.data;
  }

  // Get unread messages count
  async getUnreadCount(): Promise<{ count: number }> {
    const response = await apiService.get<{ count: number }>('/messages/unread-count');
    return response.data;
  }

  // Mute/Unmute conversation
  async toggleMute(conversationId: number): Promise<{ is_muted: boolean }> {
    const response = await apiService.post<{ is_muted: boolean }>(
      `/conversations/${conversationId}/mute`
    );
    return response.data;
  }

  // Block user in conversation
  async blockUser(conversationId: number, userId: number): Promise<ApiResponse<{ success: boolean }>> {
    return await apiService.post(`/conversations/${conversationId}/block`, { user_id: userId });
  }

  // Unblock user in conversation
  async unblockUser(conversationId: number, userId: number): Promise<ApiResponse<{ success: boolean }>> {
    return await apiService.post(`/conversations/${conversationId}/unblock`, { user_id: userId });
  }

  // Report conversation
  async reportConversation(conversationId: number, reason: string): Promise<ApiResponse<{ success: boolean }>> {
    return await apiService.post(`/conversations/${conversationId}/report`, { reason });
  }

  // Get conversation info
  async getConversationInfo(conversationId: number): Promise<{
    conversation: Conversation;
    participants: User[];
    is_muted: boolean;
    is_blocked: boolean;
  }> {
    const response = await apiService.get<{
      conversation: Conversation;
      participants: User[];
      is_muted: boolean;
      is_blocked: boolean;
    }>(`/conversations/${conversationId}/info`);
    return response.data;
  }

  // Update conversation settings
  async updateConversationSettings(
    conversationId: number,
    settings: {
      nickname?: string;
      theme?: string;
      emoji?: string;
    }
  ): Promise<Conversation> {
    const response = await apiService.put<Conversation>(
      `/conversations/${conversationId}/settings`,
      settings
    );
    return response.data;
  }

  // Get message delivery status
  async getMessageStatus(messageId: number): Promise<{
    is_delivered: boolean;
    is_read: boolean;
    delivered_at?: string;
    read_at?: string;
  }> {
    const response = await apiService.get<{
      is_delivered: boolean;
      is_read: boolean;
      delivered_at?: string;
      read_at?: string;
    }>(`/messages/${messageId}/status`);
    return response.data;
  }

  // Forward message
  async forwardMessage(
    messageId: number,
    conversationIds: number[]
  ): Promise<ApiResponse<{ success: boolean }>> {
    return await apiService.post(`/messages/${messageId}/forward`, {
      conversation_ids: conversationIds
    });
  }

  // React to message
  async reactToMessage(messageId: number, emoji: string): Promise<ApiResponse<{ success: boolean }>> {
    return await apiService.post(`/messages/${messageId}/react`, { emoji });
  }

  // Remove reaction from message
  async removeReaction(messageId: number): Promise<ApiResponse<{ success: boolean }>> {
    return await apiService.delete(`/messages/${messageId}/react`);
  }

  // Search users
  async searchUsers(query: string): Promise<ApiResponse<User[]>> {
    return await apiService.get('/users/search', { q: query });
  }
}

export const messagesService = new MessagesService();
export default messagesService;