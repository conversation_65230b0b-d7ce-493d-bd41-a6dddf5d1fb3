<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\CommentController;
use App\Http\Controllers\FollowController;
use App\Http\Controllers\HashtagController;
use App\Http\Controllers\MessageController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\PostController;
use App\Http\Controllers\UserController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public routes
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);
Route::post('/forgot-password', [AuthController::class, 'forgotPassword']);
Route::post('/reset-password', [AuthController::class, 'resetPassword']);

// Public hashtag routes
Route::get('/hashtags', [HashtagController::class, 'index']);
Route::get('/hashtags/trending', [HashtagController::class, 'trending']);
Route::get('/hashtags/search', [HashtagController::class, 'search']);
Route::get('/hashtags/{slug}', [HashtagController::class, 'show']);
Route::get('/hashtags/{slug}/related', [HashtagController::class, 'related']);

// Public user and post routes (for viewing)
Route::get('/users', [UserController::class, 'index']);
Route::get('/users/search', [UserController::class, 'search']);
Route::get('/users/{username}/posts', [UserController::class, 'posts']);
Route::get('/posts/explore', [PostController::class, 'explore']);
Route::get('/posts/search', [PostController::class, 'search']);
Route::get('/posts/feed', [PostController::class, 'index'])->middleware('auth:sanctum');

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    // Routes that are public but need auth context if available
    Route::get('/users/{username}', [UserController::class, 'show']);
    Route::get('/posts/{id}', [PostController::class, 'show']);

    // Auth routes
    Route::get('/user', [AuthController::class, 'user']);
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::post('/logout-all', [AuthController::class, 'logoutAll']);
    Route::post('/change-password', [AuthController::class, 'changePassword']);

    // User profile routes
    Route::put('/profile', [UserController::class, 'updateProfile']);
    Route::post('/profile/avatar', [UserController::class, 'updateAvatar']);
    Route::delete('/profile/avatar', [UserController::class, 'removeAvatar']);
    Route::post('/profile/cover', [UserController::class, 'updateCover']);
    Route::delete('/profile/cover', [UserController::class, 'removeCover']);
    Route::get('/users/{username}/followers', [UserController::class, 'followers']);
    Route::get('/users/{username}/following', [UserController::class, 'following']);
    Route::get('/users/suggestions', [UserController::class, 'getSuggestedUsers']);

    // Post routes
    Route::get('/posts', [PostController::class, 'index']);
    Route::post('/posts', [PostController::class, 'store']);
    Route::put('/posts/{id}', [PostController::class, 'update']);
    Route::delete('/posts/{id}', [PostController::class, 'destroy']);
    Route::post('/posts/{id}/like', [PostController::class, 'like']);
    Route::delete('/posts/{id}/like', [PostController::class, 'unlike']);

    // Comment routes
    Route::get('/posts/{postId}/comments', [CommentController::class, 'index']);
    Route::post('/posts/{postId}/comments', [CommentController::class, 'store']);
    Route::get('/comments/{id}', [CommentController::class, 'show']);
    Route::put('/comments/{id}', [CommentController::class, 'update']);
    Route::delete('/comments/{id}', [CommentController::class, 'destroy']);
    Route::post('/comments/{id}/like', [CommentController::class, 'like']);
    Route::delete('/comments/{id}/like', [CommentController::class, 'unlike']);
    Route::get('/comments/{id}/replies', [CommentController::class, 'replies']);

    // Follow routes
    Route::post('/users/{username}/follow', [FollowController::class, 'follow']);
    Route::delete('/users/{username}/follow', [FollowController::class, 'unfollow']);
    Route::get('/follow-requests/from/{followerId}', [FollowController::class, 'getFollowRequestId']);
    Route::post('/follow-requests/{id}/accept', [FollowController::class, 'acceptFollowRequest']);
    Route::post('/follow-requests/{id}/reject', [FollowController::class, 'rejectFollowRequest']);
    Route::get('/follow-requests/pending', [FollowController::class, 'pendingRequests']);
    Route::get('/follow-requests/sent', [FollowController::class, 'sentRequests']);
    Route::delete('/follow-requests/{id}/cancel', [FollowController::class, 'cancelRequest']);
    Route::delete('/followers/{id}/remove', [FollowController::class, 'removeFollower']);
    Route::get('/users/{username}/follow-status', [FollowController::class, 'followStatus']);

    // Message routes
    Route::get('/conversations', [MessageController::class, 'conversations']);
    Route::get('/conversations/{conversation}', [MessageController::class, 'getConversation']);
    Route::get('/conversations/{username}/direct', [MessageController::class, 'getOrCreateConversation']);
    Route::get('/conversations/{conversationId}/messages', [MessageController::class, 'messages']);
    Route::post('/conversations/{conversationId}/messages', [MessageController::class, 'sendMessage']);
    Route::put('/messages/{messageId}', [MessageController::class, 'editMessage']);
    Route::delete('/messages/{messageId}', [MessageController::class, 'deleteMessage']);
    Route::post('/conversations/{conversationId}/read', [MessageController::class, 'markAsRead']);
    Route::get('/users/search-for-messages', [MessageController::class, 'searchUsers']);

    // Notifications routes
    Route::get('/notifications', [NotificationController::class, 'index']);
    Route::patch('/notifications/{notification}/read', [NotificationController::class, 'markAsRead']);
    Route::patch('/notifications/mark-all-read', [NotificationController::class, 'markAllAsRead']);
    Route::get('/notifications/unread-count', [NotificationController::class, 'getUnreadCount']);
    Route::delete('/notifications/{notification}', [NotificationController::class, 'destroy']);
});
