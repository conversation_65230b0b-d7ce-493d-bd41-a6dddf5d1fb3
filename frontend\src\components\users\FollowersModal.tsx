import React, { useState, useEffect } from "react";
import { XMarkIcon } from "@heroicons/react/24/outline";
import { useAuth } from "../../contexts/AuthContext";
import { usersService } from "../../services/users";
import LoadingSpinner from "../ui/LoadingSpinner";
import { toast } from "react-hot-toast";
import type { User } from "../../types";

interface FollowersModalProps {
  userId: number;
  type: "followers" | "following";
  onClose: () => void;
}

const FollowersModal: React.FC<FollowersModalProps> = ({
  userId,
  type,
  onClose,
}) => {
  const { user: currentUser } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);

  useEffect(() => {
    fetchUsers(1);
  }, [userId, type]);

  const fetchUsers = async (pageNum: number) => {
    try {
      if (pageNum === 1) {
        setLoading(true);
        setUsers([]);
      } else {
        setLoadingMore(true);
      }

      const response = type === "followers" 
        ? await usersService.getUserFollowers(userId, pageNum)
        : await usersService.getUserFollowing(userId, pageNum);

      if (pageNum === 1) {
        setUsers(response.data);
      } else {
        setUsers(prev => [...prev, ...response.data]);
      }

      setHasMore(response.current_page < response.last_page);
      setPage(pageNum);
    } catch (error) {
      console.error(`Error fetching ${type}:`, error);
      toast.error(`Không thể tải danh sách ${type === "followers" ? "người theo dõi" : "đang theo dõi"}`);
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  const loadMore = () => {
    if (hasMore && !loadingMore) {
      fetchUsers(page + 1);
    }
  };

  const handleFollow = async (targetUser: User) => {
    try {
      const result = await usersService.toggleFollowByUsername(targetUser.username);
      
      // Update the user in the list
      setUsers(prev => prev.map(user => 
        user.id === targetUser.id 
          ? { 
              ...user, 
              follow_status: result.status || (result.is_following ? 'accepted' : null),
              followers_count: result.followers_count 
            }
          : user
      ));

      toast.success(result.is_following ? "Đã theo dõi!" : "Đã bỏ theo dõi!");
    } catch (error) {
      console.error("Error following/unfollowing user:", error);
      toast.error("Có lỗi xảy ra");
    }
  };

  const renderFollowButton = (user: User) => {
    switch (user.follow_status) {
      case "accepted":
        return (
          <button
            onClick={() => handleFollow(user)}
            className="px-4 py-1.5 text-sm font-medium border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Đang theo dõi
          </button>
        );
      case "pending":
        return (
          <button
            onClick={() => handleFollow(user)}
            className="px-4 py-1.5 text-sm font-medium border border-orange-300 text-orange-700 rounded-lg hover:bg-orange-50 transition-colors"
          >
            Chờ chấp nhận
          </button>
        );
      default:
        return (
          <button
            onClick={() => handleFollow(user)}
            className="px-4 py-1.5 text-sm font-medium bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            Theo dõi
          </button>
        );
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-md w-full max-h-[80vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold">
            {type === "followers" ? "Người theo dõi" : "Đang theo dõi"}
          </h2>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded-full transition-colors"
          >
            <XMarkIcon className="w-6 h-6 text-gray-600" />
          </button>
        </div>

        {/* Content */}
        <div className="overflow-y-auto max-h-[60vh]">
          {loading ? (
            <div className="flex justify-center items-center py-8">
              <LoadingSpinner size="lg" />
            </div>
          ) : users.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              {type === "followers" ? "Chưa có người theo dõi" : "Chưa theo dõi ai"}
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {users.map((user) => (
                <div key={user.id} className="flex items-center justify-between p-4">
                  <div className="flex items-center space-x-3">
                    <img
                      src={user.avatar || "https://api-private.atlassian.com/users/8b3597e8a7d1d8f2ed7f58bcab1946b8/avatar"}
                      alt={user.username}
                      className="w-12 h-12 rounded-full object-cover"
                    />
                    <div>
                      <p className="font-semibold text-sm">{user.username}</p>
                      {user.full_name && (
                        <p className="text-sm text-gray-500">{user.full_name}</p>
                      )}
                    </div>
                  </div>
                  
                  {/* Don't show follow button for the current user */}
                  {currentUser?.user?.id !== user.id && renderFollowButton(user)}
                </div>
              ))}
              
              {/* Load More Button */}
              {hasMore && (
                <div className="p-4">
                  <button
                    onClick={loadMore}
                    disabled={loadingMore}
                    className="w-full py-2 text-sm text-gray-600 hover:bg-gray-50 transition-colors disabled:opacity-50"
                  >
                    {loadingMore ? <LoadingSpinner size="sm" /> : "Tải thêm"}
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FollowersModal;