<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Post;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PostMedia>
 */
class PostMediaFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = \App\Models\PostMedia::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $type = fake()->randomElement(['image', 'video']);
        
        return [
            'post_id' => Post::factory(),
            'type' => $type,
            'file_path' => $type === 'image' 
                ? 'posts/images/' . fake()->uuid() . '.jpg'
                : 'posts/videos/' . fake()->uuid() . '.mp4',
            'thumbnail_path' => 'posts/thumbnails/' . fake()->uuid() . '.jpg',
            'order' => fake()->numberBetween(1, 5),
            'metadata' => [
                'width' => fake()->numberBetween(400, 1080),
                'height' => fake()->numberBetween(400, 1080),
                'size' => fake()->numberBetween(100000, 5000000),
            ],
            'created_at' => fake()->dateTimeBetween('-6 months', 'now'),
            'updated_at' => function (array $attributes) {
                return $attributes['created_at'];
            },
        ];
    }
}