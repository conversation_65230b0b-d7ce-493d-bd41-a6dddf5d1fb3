import { io, Socket } from 'socket.io-client';
import type { Message, User, Notification, Comment } from '../types';

// <PERSON><PERSON>nh nghĩa giao diện cho các sự kiện socket
interface SocketEvents {
  new_message: (message: Message) => void;
  message_read: (data: { conversation_id: number; message_ids: number[]; user_id: number }) => void;
  user_typing: (data: { conversation_id: number; user: User; is_typing: boolean }) => void;
  user_online: (data: { user_id: number; is_online: boolean; last_seen?: string }) => void;
  message_delivered: (data: { message_id: number; delivered_at: string }) => void;
  conversation_updated: (data: { conversation_id: number; type: string; data: unknown }) => void;
  new_notification: (notification: Notification) => void;
  post_like_updated: (data: { post_id: number; likes_count: number; is_liked: boolean }) => void;
  new_post_available: (data: { message: string }) => void;
  post_comment_added: (data: { post_id: number; comment: Comment }) => void;
  post_share_updated: (data: { post_id: number; shares_count: number }) => void;
  incoming_call: (data: { caller: User; call_id: string; type: 'video' | 'audio' }) => void;
  call_ended: (data: { call_id: string; reason?: string }) => void;
  user_updated: (updatedUser: User) => void; // Sự kiện cập nhật người dùng
  connected: () => void;
  disconnected: (reason: string) => void;
  error: (error: Error) => void;
  max_reconnect_attempts_reached: () => void;
}

class SocketService {
  private socket: Socket | null = null; // Đối tượng socket
  private isConnected = false; // Trạng thái kết nối
  private reconnectAttempts = 0; // Số lần thử kết nối lại
  private maxReconnectAttempts = 5; // Số lần thử tối đa
  private reconnectDelay = 1000; // Độ trễ giữa các lần thử (ms)

  // Danh sách các trình nghe sự kiện
  private eventListeners = {} as Record<keyof SocketEvents, SocketEvents[keyof SocketEvents][]>;

  // Kết nối tới server socket
  connect(token: string, userId?: number): void {
    if (this.socket?.connected) {
      return;
    }

    this.socket = io(import.meta.env.VITE_SOCKET_URL, {
      auth: {
        token,
        userId,
      },
      transports: ['websocket', 'polling'],
      timeout: 20000,
      forceNew: true,
    });

    this.thietLapTrinhNgheSuKien();
  }

  // Thiết lập các trình nghe sự kiện socket
  private thietLapTrinhNgheSuKien(): void {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('Socket đã kết nối:', this.socket?.id);
      this.isConnected = true;
      this.reconnectAttempts = 0;
      this.phatSuKien('connected');
    });

    this.socket.on('disconnect', (lyDo) => {
      console.log('Socket đã ngắt kết nối:', lyDo);
      this.isConnected = false;
      this.phatSuKien('disconnected', lyDo);

      if (lyDo === 'io server disconnect') {
        this.xuLyKetNoiLai();
      }
    });

    this.socket.on('connect_error', (loi) => {
      console.error('Lỗi kết nối socket:', loi);
      this.phatSuKien('error', loi);
      this.xuLyKetNoiLai();
    });

    // Sự kiện tin nhắn
    this.socket.on('new_message', (tinNhan: Message) => {
      console.log('Socket nhận tin nhắn mới:', tinNhan);
      this.phatSuKien('new_message', tinNhan);
    });

    this.socket.on('message_read', (duLieu: { conversation_id: number; message_ids: number[]; user_id: number }) => {
      this.phatSuKien('message_read', duLieu);
    });

    this.socket.on('user_typing', (duLieu: { conversation_id: number; user: User; is_typing: boolean }) => {
      this.phatSuKien('user_typing', duLieu);
    });

    this.socket.on('user_online', (duLieu: { user_id: number; is_online: boolean; last_seen?: string }) => {
      this.phatSuKien('user_online', duLieu);
    });

    this.socket.on('message_delivered', (duLieu: { message_id: number; delivered_at: string }) => {
      this.phatSuKien('message_delivered', duLieu);
    });

    this.socket.on('conversation_updated', (duLieu: { conversation_id: number; type: string; data: unknown }) => {
      this.phatSuKien('conversation_updated', duLieu);
    });

    // Sự kiện thông báo
    this.socket.on('new_notification', (thongBao: Notification) => {
      this.phatSuKien('new_notification', thongBao);
    });

    // Sự kiện bài viết
    this.socket.on('post_like_updated', (duLieu: { post_id: number; likes_count: number; is_liked: boolean }) => {
      this.phatSuKien('post_like_updated', duLieu);
    });

    this.socket.on('new_post_available', (duLieu: { message: string }) => {
      this.phatSuKien('new_post_available', duLieu);
    });

    this.socket.on('post_comment_added', (duLieu: { post_id: number; comment: Comment }) => {
      this.phatSuKien('post_comment_added', duLieu);
    });

    this.socket.on('post_share_updated', (duLieu: { post_id: number; shares_count: number }) => {
      this.phatSuKien('post_share_updated', duLieu);
    });

    // Sự kiện cập nhật người dùng
    this.socket.on('user_updated', (nguoiDungCapNhat: User) => {
      console.log('Socket nhận sự kiện user_updated:', nguoiDungCapNhat);
      this.phatSuKien('user_updated', nguoiDungCapNhat);
    });

    // Sự kiện cuộc gọi
    this.socket.on('incoming_call', (duLieu: { caller: User; call_id: string; type: 'video' | 'audio' }) => {
      this.phatSuKien('incoming_call', duLieu);
    });

    this.socket.on('call_ended', (duLieu: { call_id: string; reason?: string }) => {
      this.phatSuKien('call_ended', duLieu);
    });
  }

  // Xử lý kết nối lại
  private xuLyKetNoiLai(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Đã đạt số lần thử kết nối lại tối đa');
      this.phatSuKien('max_reconnect_attempts_reached');
      return;
    }

    this.reconnectAttempts++;
    const doTre = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // Tăng độ trễ theo cấp số nhân

    setTimeout(() => {
      console.log(`Đang thử kết nối lại... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      this.socket?.connect();
    }, doTre);
  }

  // Ngắt kết nối socket
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
    }
  }

  // Tham gia phòng hội thoại
  joinConversation(conversationId: number): void {
    if (this.socket?.connected) {
      this.socket.emit('join_conversation', { conversation_id: conversationId });
    }
  }

  // Rời phòng hội thoại
  leaveConversation(conversationId: number): void {
    if (this.socket?.connected) {
      this.socket.emit('leave_conversation', { conversation_id: conversationId });
    }
  }

  // Gửi chỉ báo đang nhập
  sendTyping(conversationId: number, isTyping: boolean): void {
    if (this.socket?.connected) {
      this.socket.emit('typing', {
        conversation_id: conversationId,
        is_typing: isTyping,
      });
    }
  }

  // Đánh dấu tin nhắn đã đọc
  markAsRead(conversationId: number, messageIds: number[]): void {
    if (this.socket?.connected) {
      this.socket.emit('mark_as_read', {
        conversation_id: conversationId,
        message_ids: messageIds,
      });
    }
  }

  // Cập nhật trạng thái trực tuyến
  updateOnlineStatus(isOnline: boolean): void {
    if (this.socket?.connected) {
      this.socket.emit('update_status', { is_online: isOnline });
    }
  }

  // Gửi tin nhắn qua socket
  sendMessage(conversationId: number, content: string, sender: User, tempId?: string): void {
    if (this.socket?.connected) {
      this.socket.emit('new_message', {
        conversation_id: conversationId,
        content,
        sender,
        temp_id: tempId,
      });
    }
  }

  // Đăng ký trình nghe sự kiện
  on<T extends keyof SocketEvents>(event: T, callback: SocketEvents[T]): void {
    if (!this.eventListeners[event]) {
      this.eventListeners[event] = [] as SocketEvents[T][];
    }
    (this.eventListeners[event] as SocketEvents[T][]).push(callback);
  }

  // Hủy đăng ký trình nghe sự kiện
  off<T extends keyof SocketEvents>(event: T, callback?: SocketEvents[T]): void {
    if (!this.eventListeners[event]) return;

    if (callback) {
      const listeners = this.eventListeners[event] as SocketEvents[T][];
      if (listeners) {
        this.eventListeners[event] = listeners.filter(cb => cb !== callback) as SocketEvents[T][];
      }
    } else {
      this.eventListeners[event] = [] as SocketEvents[T][];
    }
  }

  // Phát sự kiện tới các trình nghe
  private phatSuKien<T extends keyof SocketEvents>(event: T, data?: Parameters<SocketEvents[T]>[0]): void {
    if (this.eventListeners[event]) {
      this.eventListeners[event]!.forEach(callback => {
        try {
          callback(data as any);
        } catch (error) {
          console.error(`Lỗi trong trình nghe sự kiện socket cho ${event}:`, error);
        }
      });
    }
  }

  // Getter cho trạng thái kết nối
  get connected(): boolean {
    return this.isConnected && this.socket?.connected === true;
  }

  // Getter cho ID socket
  get socketId(): string | undefined {
    return this.socket?.id;
  }

  // Kiểm tra sự kiện có được hỗ trợ không
  isEventSupported(event: string): boolean {
    const suKienHoTro = [
      'connected',
      'disconnected',
      'error',
      'new_message',
      'message_read',
      'user_typing',
      'user_online',
      'message_delivered',
      'conversation_updated',
      'new_notification',
      'incoming_call',
      'call_ended',
      'max_reconnect_attempts_reached',
      'user_updated', // Thêm sự kiện này
    ];
    return suKienHoTro.includes(event);
  }

  // Lấy trạng thái kết nối để debug
  getConnectionState(): {
    connected: boolean;
    socketId?: string;
    reconnectAttempts: number;
    eventListeners: string[];
  } {
    return {
      connected: this.connected,
      socketId: this.socketId,
      reconnectAttempts: this.reconnectAttempts,
      eventListeners: Object.keys(this.eventListeners),
    };
  }
}

export const socketService = new SocketService();
export default socketService;