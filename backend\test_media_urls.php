<?php

$url = 'http://127.0.0.1:8001/api/posts';
$token = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vMTI3LjAuMC4xOjgwMDEvYXBpL2xvZ2luIiwiaWF0IjoxNzM3NzE0NzI5LCJleHAiOjE3Mzc3MTgzMjksIm5iZiI6MTczNzcxNDcyOSwianRpIjoiVGNGNGJGNGNGNGJGNGNGNCIsInN1YiI6IjEiLCJwcnYiOiIyM2JkNWM4OTQ5ZjYwMGFkYjM5ZTcwMWM0MDA4NzJkYjdhNTk3NmY3In0.abc123';

$context = stream_context_create([
    'http' => [
        'header' => "Authorization: Bearer $token"
    ]
]);

$response = file_get_contents($url, false, $context);

if ($response === false) {
    echo "Failed to fetch data from API\n";
    exit(1);
}

$data = json_decode($response, true);

if (!isset($data['data']) || !is_array($data['data'])) {
    echo "No posts found or invalid response\n";
    echo "Response: " . $response . "\n";
    exit(1);
}

echo "Testing media URLs...\n";
echo "===================\n";

foreach ($data['data'] as $post) {
    if (isset($post['media']) && is_array($post['media'])) {
        foreach ($post['media'] as $media) {
            echo "Media ID: " . $media['id'] . "\n";
            echo "File URL: " . $media['file_url'] . "\n";
            echo "Thumbnail URL: " . $media['thumbnail_url'] . "\n";
            
            // Check if URLs contain full domain
            $hasFullDomain = strpos($media['file_url'], 'http://localhost:8001') === 0;
            echo "Has full domain: " . ($hasFullDomain ? 'YES' : 'NO') . "\n";
            echo "---\n";
        }
    }
}

echo "Test completed.\n";