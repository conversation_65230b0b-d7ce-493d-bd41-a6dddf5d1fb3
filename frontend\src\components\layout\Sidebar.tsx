import React, { useState, useEffect } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { useAuth } from "../../contexts/AuthContext";
import { useNotifications } from "../../hooks/useNotifications";
import { useSidebar } from "../../contexts/SidebarContext";
import {
  HomeIcon,
  MagnifyingGlassIcon,
  PlusIcon,
  ChatBubbleLeftIcon,
  HeartIcon,
  UserIcon,
  Bars3Icon,
  Cog6ToothIcon,
  ArrowLeftOnRectangleIcon,
} from "@heroicons/react/24/outline";
import {
  HomeIcon as HomeIconSolid,
  MagnifyingGlassIcon as SearchIconSolid,
  ChatBubbleLeftIcon as MessageIconSolid,
  HeartIcon as HeartIconSolid,
  UserIcon as UserIconSolid,
} from "@heroicons/react/24/solid";
import toast from "react-hot-toast";
import logo from "../../assets/logo.png";
const Sidebar: React.FC = () => {
  const { user, logout } = useAuth();
  console.log("user", user);
  const { unreadCount } = useNotifications();
  const { isCollapsed, setIsCollapsed } = useSidebar();
  const location = useLocation();
  const navigate = useNavigate();
  const [showMenu, setShowMenu] = useState(false);

  // Remove auto-collapse logic to prevent unwanted spacing when on notifications page

  const handleLogout = async () => {
    try {
      await logout();
      toast.success("Đăng xuất thành công!");
      navigate("/login");
    } catch {
      toast.error("Có lỗi xảy ra khi đăng xuất");
    }
  };

  const menuItems = [
    {
      name: "Trang chủ",
      path: "/",
      icon: HomeIcon,
      activeIcon: HomeIconSolid,
    },
    {
      name: "Tìm kiếm",
      path: "/explore",
      icon: MagnifyingGlassIcon,
      activeIcon: SearchIconSolid,
    },
    {
      name: "Tạo",
      path: "/create",
      icon: PlusIcon,
      activeIcon: PlusIcon,
    },
    {
      name: "Tin nhắn",
      path: "/messages",
      icon: ChatBubbleLeftIcon,
      activeIcon: MessageIconSolid,
    },
    {
      name: "Thông báo",
      path: "/notifications",
      icon: HeartIcon,
      activeIcon: HeartIconSolid,
      hasNotification: true,
    },
    {
      name: "Hồ sơ",
      path: `/${user?.user?.username || user?.username}`,
      icon: UserIcon,
      activeIcon: UserIconSolid,
    },
  ];

  return (
    <div
      className={`fixed left-0 top-0 h-full bg-white border-r border-gray-200 z-50 transition-all duration-300 ${
        isCollapsed ? "w-16" : "w-64 lg:w-72"
      }`}
    >
      <div className="flex flex-col h-full">
        {/* Logo */}
        <div
          className={`border-b border-gray-200 ${isCollapsed ? "p-4" : "p-6"}`}
        >
          <Link to="/" className="block">
            {isCollapsed ? (
              <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                <img src={logo} alt="logo" className="w-8 h-8" />
              </div>
            ) : (
              // <h1 className="text-2xl font-bold text-gray-900">Instagram</h1>
              <img src={logo} alt="" />
            )}
          </Link>
        </div>

        {/* Navigation */}
        <nav className={`flex-1 py-6 ${isCollapsed ? "px-2" : "px-4"}`}>
          <ul className="space-y-2">
            {menuItems.map((item) => {
              const isActive = location.pathname === item.path;
              const Icon = isActive ? item.activeIcon : item.icon;
              const showBadge = item.hasNotification && unreadCount > 0;

              return (
                <li key={item.name}>
                  <Link
                    to={item.path}
                    className={`flex items-center rounded-lg transition-colors duration-200 ${
                      isCollapsed ? "p-3 justify-center" : "px-4 py-3"
                    } ${
                      isActive
                        ? "bg-gray-100 text-gray-900 font-semibold"
                        : "text-gray-700 hover:bg-gray-50"
                    }`}
                    title={isCollapsed ? item.name : undefined}
                  >
                    <div className={`relative ${isCollapsed ? "" : "mr-4"}`}>
                      <Icon className="w-6 h-6" />
                      {showBadge && (
                        <div
                          className={`absolute bg-red-500 text-white text-xs rounded-full min-w-[18px] h-[18px] flex items-center justify-center px-1 ${
                            isCollapsed ? "-top-1 -right-1" : "-top-2 -right-2"
                          }`}
                        >
                          {unreadCount > 99 ? "99+" : unreadCount}
                        </div>
                      )}
                    </div>
                    {!isCollapsed && (
                      <span className="text-base">{item.name}</span>
                    )}
                  </Link>
                </li>
              );
            })}
          </ul>
        </nav>

        {/* User Menu */}
        {!isCollapsed && (
          <div className="relative px-4 py-6 border-t border-gray-200">
            <button
              onClick={() => setShowMenu(!showMenu)}
              className="flex items-center w-full px-4 py-3 rounded-lg hover:bg-gray-50 transition-colors duration-200"
            >
              <Bars3Icon className="w-6 h-6 mr-4 text-gray-700" />
              <span className="text-base text-gray-700">Thêm</span>
            </button>

            {showMenu && (
              <div className="absolute bottom-full left-4 right-4 mb-2 bg-white border border-gray-200 rounded-lg shadow-lg">
                <button
                  onClick={handleLogout}
                  className="flex items-center w-full px-4 py-3 hover:bg-gray-50 transition-colors duration-200 text-left"
                >
                  <ArrowLeftOnRectangleIcon className="w-5 h-5 mr-3 text-gray-700" />
                  <span className="text-sm text-gray-700">Đăng xuất</span>
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default Sidebar;
