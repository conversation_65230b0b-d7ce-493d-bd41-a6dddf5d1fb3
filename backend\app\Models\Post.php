<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Post extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'caption',
        'location',
        'comments_disabled',
        'likes_disabled',
        'likes_count',
        'comments_count',
        'shares_count',
    ];

    protected $casts = [
        'comments_disabled' => 'boolean',
        'likes_disabled' => 'boolean',
        'likes_count' => 'integer',
        'comments_count' => 'integer',
        'shares_count' => 'integer',
    ];

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function media()
    {
        return $this->hasMany(PostMedia::class)->orderBy('order');
    }

    public function likes()
    {
        return $this->morphMany(Like::class, 'likeable');
    }

    public function comments()
    {
        return $this->hasMany(Comment::class)->whereNull('parent_id')->orderBy('created_at', 'desc');
    }

    public function allComments()
    {
        return $this->hasMany(Comment::class);
    }

    public function hashtags()
    {
        return $this->belongsToMany(Hashtag::class, 'post_hashtags');
    }

    // Helper methods
    public function isLikedBy(User $user)
    {
        return $this->likes()->where('user_id', $user->id)->exists();
    }

    public function toggleLike(User $user)
    {
        $like = $this->likes()->where('user_id', $user->id)->first();

        if ($like) {
            $like->delete();
            $this->decrement('likes_count');
            return false;
        } else {
            $this->likes()->create(['user_id' => $user->id]);
            $this->increment('likes_count');
            return true;
        }
    }

    public function extractHashtags()
    {
        if (!$this->caption) {
            return [];
        }

        preg_match_all('/#([a-zA-Z0-9_\x{00a1}-\x{ffff}]+)/u', $this->caption, $matches);
        return $matches[1] ?? [];
    }

    public function syncHashtags()
    {
        $hashtagNames = $this->extractHashtags();
        $hashtagIds = [];

        foreach ($hashtagNames as $name) {
            $hashtag = Hashtag::firstOrCreate(
                ['name' => $name],
                ['slug' => \Illuminate\Support\Str::slug($name)]
            );
            $hashtagIds[] = $hashtag->id;
        }

        $this->hashtags()->sync($hashtagIds);
    }

    protected static function boot()
    {
        parent::boot();

        static::created(function ($post) {
            $post->syncHashtags();
        });

        static::updated(function ($post) {
            if ($post->isDirty('caption')) {
                $post->syncHashtags();
            }
        });
    }
}
