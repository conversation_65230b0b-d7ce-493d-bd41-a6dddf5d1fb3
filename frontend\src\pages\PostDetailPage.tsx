import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";
import type { Post } from "../types";
import PostCard from "../components/posts/PostCard";
import CommentSection from "../components/posts/CommentSection";
import LoadingSpinner from "../components/ui/LoadingSpinner";
import { ChevronLeftIcon } from "@heroicons/react/24/outline";
import { toast } from "react-hot-toast";
import { postsService } from "../services/posts";

const PostDetailPage: React.FC = () => {
  const { postId } = useParams<{ postId: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [post, setPost] = useState<Post | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (postId) {
      fetchPost();
    }
  }, [postId]);

  const fetchPost = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!postId) {
        setError("ID bài viết không hợp lệ");
        return;
      }

      // Fetch real post data from API
      const postData = await postsService.getPost(Number(postId));
      setPost(postData);
    } catch (error) {
      console.error("Error fetching post:", error);
      setError("Không thể tải bài viết");
      toast.error("Không thể tải bài viết");
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    navigate(-1);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error || !post) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            {error || "Không tìm thấy bài viết"}
          </h2>
          <p className="text-gray-600 mb-6">
            Bài viết này có thể đã bị xóa hoặc không tồn tại.
          </p>
          <div className="space-x-4">
            <button
              onClick={handleBack}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Quay lại
            </button>
            <button
              onClick={() => navigate("/")}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              Về trang chủ
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-4 py-3 sticky top-0 z-10">
        <div className="flex items-center space-x-4 max-w-4xl mx-auto">
          <button
            onClick={handleBack}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <ChevronLeftIcon className="w-6 h-6" />
          </button>
          <h1 className="text-xl font-semibold">Bài viết</h1>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto">
        {/* Desktop Layout */}
        <div className="hidden lg:grid lg:grid-cols-3 lg:gap-8 lg:p-8">
          {/* Post Media */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-sm overflow-hidden">
              <div className="aspect-square bg-black flex items-center justify-center">
                {post.media[0].type === "image" ? (
                  <img
                    src={post.media[0].file_url}
                    alt="Post"
                    className="max-w-full max-h-full object-contain"
                  />
                ) : (
                  <video
                    src={post.media[0].file_url}
                    controls
                    className="max-w-full max-h-full object-contain"
                  />
                )}
              </div>
            </div>
          </div>

          {/* Post Details and Comments */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm h-full flex flex-col">
              {/* Post Header */}
              <div className="p-4 border-b border-gray-200">
                <div className="flex items-center space-x-3">
                  <img
                    src={
                      post.user.avatar ||
                      "https://api-private.atlassian.com/users/8b3597e8a7d1d8f2ed7f58bcab1946b8/avatar"
                    }
                    alt={post.user.username}
                    className="w-10 h-10 rounded-full object-cover"
                  />
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <span className="font-semibold text-sm">
                        {post.user.username}
                      </span>
                      {post.location && (
                        <>
                          <span className="text-gray-400">•</span>
                          <span className="text-gray-600 text-sm">
                            {post.location}
                          </span>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Comments Section */}
              <div className="flex-1 overflow-hidden">
                <CommentSection postId={post.id} postOwnerId={post.user_id} />
              </div>
            </div>
          </div>
        </div>

        {/* Mobile Layout */}
        <div className="lg:hidden">
          <div className="bg-white">
            <PostCard
              post={post}
              onLike={() => {
                // Handle like functionality
                console.log("Like clicked");
              }}
              onSave={() => {
                // Handle save functionality
                console.log("Save clicked");
              }}
            />
          </div>

          {/* Mobile Comments */}
          <div className="bg-white mt-1">
            <CommentSection postId={post.id} postOwnerId={post.user_id} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default PostDetailPage;
