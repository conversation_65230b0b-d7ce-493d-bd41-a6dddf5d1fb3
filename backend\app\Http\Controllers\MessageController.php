<?php

namespace App\Http\Controllers;

use App\Models\Conversation;
use App\Models\Message;
use App\Models\User;
use App\Models\Post;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;

class MessageController extends Controller
{
    public function conversations(Request $request)
    {
        $user = $request->user();

        $conversations = $user->conversations()
            ->with(['participants', 'latestMessage.user'])
            ->orderBy('last_message_at', 'desc')
            ->paginate(20);

        // Add unread count and other participant info for each conversation
        $conversations->getCollection()->transform(function ($conversation) use ($user) {
            $conversation->unread_count = $conversation->getUnreadCount($user);

            if ($conversation->isDirect()) {
                $conversation->other_participant = $conversation->getOtherParticipant($user);
            }

            return $conversation;
        });

        return response()->json([
            'success' => true,
            'data' => $conversations
        ]);
    }

    public function getOrCreateConversation(Request $request, $username)
    {
        $otherUser = User::where('username', $username)->firstOrFail();
        $currentUser = $request->user();

        if ($otherUser->id === $currentUser->id) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot create conversation with yourself'
            ], 400);
        }

        $conversation = Conversation::findOrCreateDirectConversation($currentUser, $otherUser);

        $conversation->load(['participants', 'latestMessage.user']);
        $conversation->other_participant = $conversation->getOtherParticipant($currentUser);
        $conversation->unread_count = $conversation->getUnreadCount($currentUser);

        return response()->json([
            'success' => true,
            'data' => $conversation
        ]);
    }

    public function getConversation(Request $request, $conversationId)
    {
        $conversation = Conversation::findOrFail($conversationId);
        $user = $request->user();

        // Check if user is participant
        if (!$conversation->hasParticipant($user)) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        $conversation->load(['participants', 'latestMessage.user']);
        $conversation->other_participant = $conversation->getOtherParticipant($user);
        $conversation->unread_count = $conversation->getUnreadCount($user);

        return response()->json([
            'success' => true,
            'data' => $conversation
        ]);
    }

    public function messages(Request $request, $conversationId)
    {
        $conversation = Conversation::findOrFail($conversationId);
        $user = $request->user();

        // Check if user is participant
        if (!$conversation->hasParticipant($user)) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        $messages = $conversation->messages()
            ->with(['user'])
            ->orderBy('created_at', 'desc')
            ->orderBy('id', 'desc')
            ->paginate(50);

        // Mark conversation as read
        $conversation->markAsRead($user);

        return response()->json([
            'success' => true,
            'data' => $messages
        ]);
    }

    public function sendMessage(Request $request, $conversationId)
    {
        $conversation = Conversation::findOrFail($conversationId);
        $user = $request->user();

        // Check if user is participant
        if (!$conversation->hasParticipant($user)) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'type' => 'sometimes|in:text,image,video,post_share',
            'content' => 'required_without:file,post_id|string|max:1000',
            'file' => 'required_if:type,image,video|file|mimes:jpeg,png,jpg,gif,mp4,mov,avi|max:51200',
            'post_id' => 'required_if:type,post_share|exists:posts,id',
        ]);

        // Set default type to text if not provided
        if (!$request->has('type')) {
            $request->merge(['type' => 'text']);
        }

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        DB::beginTransaction();

        try {
            $messageData = [
                'conversation_id' => $conversation->id,
                'user_id' => $user->id,
                'type' => $request->type,
            ];

            switch ($request->type) {
                case 'text':
                    $messageData['content'] = $request->content;
                    break;

                case 'image':
                case 'video':
                    $file = $request->file('file');
                    $filePath = $file->store('messages', 'public');

                    $messageData['file_path'] = $filePath;
                    $messageData['metadata'] = [
                        'file_size' => $file->getSize(),
                        'mime_type' => $file->getMimeType(),
                        'original_name' => $file->getClientOriginalName(),
                    ];

                    // For images, get dimensions
                    if ($request->type === 'image') {
                        $imagePath = Storage::disk('public')->path($filePath);
                        $imageSize = getimagesize($imagePath);
                        if ($imageSize) {
                            $messageData['metadata']['width'] = $imageSize[0];
                            $messageData['metadata']['height'] = $imageSize[1];
                        }
                    }
                    break;

                case 'post_share':
                    $post = Post::findOrFail($request->post_id);
                    $messageData['metadata'] = [
                        'post_id' => $post->id,
                        'post_caption' => $post->caption,
                        'post_user' => $post->user->username,
                    ];

                    // Increment post shares count
                    $post->increment('shares_count');

                    // Emit socket event for realtime updates
                    $this->emitSocketEvent('post_shared', [
                        'post_id' => $post->id,
                        'shared_by_user_id' => $request->user()->id,
                        'shared_by_user' => $request->user()->only(['id', 'username', 'name', 'avatar']),
                        'shares_count' => $post->fresh()->shares_count,
                        'conversation_id' => $conversation->id,
                        'timestamp' => now()->toISOString()
                    ]);
                    break;
            }

            $message = Message::create($messageData);
            $message->load(['user']);

            // Emit socket event for the new message (exclude sender)
            $room = 'conversation_' . $conversation->id;
            $messageData = $message->toArray();
            $messageData['exclude_user_id'] = $user->id; // Add sender ID to exclude from broadcast
            $this->emitSocketEvent('new_message', $messageData, $room);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Message sent successfully',
                'data' => $message
            ], 201);
        } catch (\Exception $e) {
            DB::rollback();

            return response()->json([
                'success' => false,
                'message' => 'Failed to send message',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function editMessage(Request $request, $messageId)
    {
        $message = Message::findOrFail($messageId);
        $user = $request->user();

        // Check if user owns the message
        if ($message->user_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        if ($message->type !== 'text') {
            return response()->json([
                'success' => false,
                'message' => 'Only text messages can be edited'
            ], 400);
        }

        $validator = Validator::make($request->all(), [
            'content' => 'required|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $message->edit($request->content);

        return response()->json([
            'success' => true,
            'message' => 'Message edited successfully',
            'data' => $message
        ]);
    }

    public function deleteMessage(Request $request, $messageId)
    {
        $message = Message::findOrFail($messageId);
        $user = $request->user();

        // Check if user owns the message
        if ($message->user_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        $message->delete();

        return response()->json([
            'success' => true,
            'message' => 'Message deleted successfully'
        ]);
    }

    public function markAsRead(Request $request, $conversationId)
    {
        $conversation = Conversation::findOrFail($conversationId);
        $user = $request->user();

        // Check if user is participant
        if (!$conversation->hasParticipant($user)) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        $conversation->markAsRead($user);

        return response()->json([
            'success' => true,
            'message' => 'Conversation marked as read'
        ]);
    }

    public function searchUsers(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'q' => 'required|string|min:1',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $query = $request->q;
        $currentUser = $request->user();

        $users = User::where('id', '!=', $currentUser->id)
            ->where(function ($q) use ($query) {
                $q->where('username', 'like', "%{$query}%")
                    ->orWhere('name', 'like', "%{$query}%");
            })
            ->select(['id', 'username', 'name', 'avatar'])
            ->limit(20)
            ->get();

        return response()->json([
            'success' => true,
            'data' => $users
        ]);
    }

    /**
     * Emit socket event to Socket.IO server
     */
    private function emitSocketEvent($event, $data, $room = null)
    {
        // Make sure to configure your socket server URL in .env
        $socketUrl = config('app.socket_server_url');
        if ($socketUrl) {
            try {
                $payload = [
                    'event' => $event,
                    'data' => $data,
                ];
                if ($room) {
                    $payload['room'] = $room;
                }
                Http::post("{$socketUrl}/api/emit", $payload);
            } catch (\Exception $e) {
                // Log the error, but don't fail the request
                // Log::error("Failed to emit socket event '{$event}': " . $e->getMessage());
            }
        }
    }
}
