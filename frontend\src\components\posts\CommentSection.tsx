import React, { useState, useEffect, useCallback } from "react";
import type { Comment } from "../../types";
import {
  HeartIcon,
  EllipsisHorizontalIcon,
  FaceSmileIcon,
  PencilIcon,
  TrashIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import { HeartIcon as HeartIconSolid } from "@heroicons/react/24/solid";
import { formatDistanceToNow } from "date-fns";
import { vi } from "date-fns/locale";
import LoadingSpinner from "../ui/LoadingSpinner";
import { toast } from "react-hot-toast";
import { apiService } from "../../services/api";
import { useAuth } from "../../contexts/AuthContext";
import { socketService } from "../../services/socket";

interface CommentSectionProps {
  postId: number;
  postOwnerId?: number;
  onClose?: () => void;
}

const CommentSection: React.FC<CommentSectionProps> = ({
  postId,
  postOwnerId,
  onClose,
}) => {
  console.log("postOwnerId", postOwnerId);
  const { user } = useAuth();
  const [comments, setComments] = useState<Comment[]>([]);
  const [loading, setLoading] = useState(true);
  const [newComment, setNewComment] = useState("");
  const [submitting, setSubmitting] = useState(false);
  const [replyingTo, setReplyingTo] = useState<number | null>(null);
  const [replyContent, setReplyContent] = useState("");
  const [editingComment, setEditingComment] = useState<number | null>(null);
  const [editContent, setEditContent] = useState("");
  const [showDropdown, setShowDropdown] = useState<number | null>(null);
  const [deletingComment, setDeletingComment] = useState<number | null>(null);

  const fetchComments = useCallback(async () => {
    try {
      setLoading(true);
      const response = await apiService.getComments(postId);
      setComments(response.data || []);
    } catch (error) {
      console.error("Error fetching comments:", error);
      toast.error("Không thể tải bình luận");
    } finally {
      setLoading(false);
    }
  }, [postId]);

  useEffect(() => {
    fetchComments();
  }, [fetchComments]);

  // Socket listener for real-time comment updates
  useEffect(() => {
    const handlePostCommentAdded = (data: {
      post_id: number;
      comment: Comment;
      user: any;
      timestamp: string;
    }) => {
      // Only update if this is for the current post
      if (data.post_id === postId) {
        setComments((prev) => {
          // Check if it's a reply (has parent_id)
          if (data.comment.parent_id) {
            // Add reply to the parent comment's replies array
            return prev.map((comment) => {
              if (comment.id === data.comment.parent_id) {
                const replyExists = comment.replies?.some(
                  (r) => r.id === data.comment.id
                );
                if (!replyExists) {
                  return {
                    ...comment,
                    replies: [...(comment.replies || []), data.comment],
                    replies_count: (comment.replies_count || 0) + 1,
                  };
                }
              }
              return comment;
            });
          } else {
            // It's a main comment
            const exists = prev.some((c) => c.id === data.comment.id);
            if (!exists) {
              return [...prev, data.comment];
            }
          }
          return prev;
        });
      }
    };

    // Register socket listener
    socketService.on("post_comment_added", handlePostCommentAdded);

    // Cleanup
    return () => {
      socketService.off("post_comment_added", handlePostCommentAdded);
    };
  }, [postId]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (showDropdown !== null && !target.closest(".dropdown-container")) {
        setShowDropdown(null);
      }
    };

    if (showDropdown !== null) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showDropdown]);

  const handleSubmitComment = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newComment.trim() || submitting) return;

    setSubmitting(true);
    try {
      const response = await apiService.createComment(
        postId,
        newComment.trim()
      );
      if (response.success) {
        // Don't add comment to local state here - let socket handle it
        // This prevents duplicate comments for the sender
        setNewComment("");
        toast.success("Đã thêm bình luận!");
      }
    } catch (error) {
      console.error("Error creating comment:", error);
      toast.error("Có lỗi xảy ra khi thêm bình luận");
    } finally {
      setSubmitting(false);
    }
  };

  const handleLikeComment = async (commentId: string) => {
    try {
      const comment = comments.find((c) => c.id === Number(commentId));
      if (!comment) return;

      // Optimistic update
      setComments((prev) =>
        prev.map((c) =>
          c.id === Number(commentId)
            ? {
                ...c,
                is_liked: !c.is_liked,
                likes_count: c.is_liked
                  ? Math.max(0, c.likes_count - 1)
                  : c.likes_count + 1,
              }
            : c
        )
      );

      // Call API
      if (comment.is_liked) {
        await apiService.unlikeComment(commentId);
      } else {
        await apiService.likeComment(commentId);
      }
    } catch (error) {
      console.error("Error liking comment:", error);
      // Revert optimistic update on error
      setComments((prev) =>
        prev.map((c) =>
          c.id === Number(commentId)
            ? {
                ...c,
                is_liked: !c.is_liked,
                likes_count: c.is_liked
                  ? Math.max(0, c.likes_count - 1)
                  : c.likes_count + 1,
              }
            : c
        )
      );
      toast.error("Có lỗi xảy ra khi thích bình luận");
    }
  };

  const handleReplySubmit = async (e: React.FormEvent, parentId: number) => {
    e.preventDefault();
    if (!replyContent.trim() || submitting) return;

    setSubmitting(true);
    try {
      const response = await apiService.createComment(
        postId,
        replyContent.trim(),
        parentId
      );
      if (response.success) {
        // Don't add reply to local state here - let socket handle it
        // This prevents duplicate replies for the sender
        setReplyContent("");
        setReplyingTo(null);
        toast.success("Đã trả lời bình luận!");
      }
    } catch (error) {
      console.error("Error replying to comment:", error);
      toast.error("Có lỗi xảy ra khi trả lời bình luận");
    } finally {
      setSubmitting(false);
    }
  };

  const handleEditSubmit = async (e: React.FormEvent, commentId: number) => {
    e.preventDefault();
    if (!editContent.trim() || submitting) return;

    setSubmitting(true);
    try {
      const response = await apiService.updateComment(
        commentId.toString(),
        editContent.trim()
      );
      if (response.success) {
        setComments((prev) =>
          prev.map((comment) => {
            // Check if it's the main comment
            if (comment.id === commentId) {
              return { ...comment, content: editContent.trim() };
            }
            // Check if it's a reply
            if (comment.replies) {
              return {
                ...comment,
                replies: comment.replies.map((reply) =>
                  reply.id === commentId
                    ? { ...reply, content: editContent.trim() }
                    : reply
                ),
              };
            }
            return comment;
          })
        );
        setEditContent("");
        setEditingComment(null);
        toast.success("Đã cập nhật bình luận!");
      }
    } catch (error) {
      console.error("Error updating comment:", error);
      toast.error("Có lỗi xảy ra khi cập nhật bình luận");
    } finally {
      setSubmitting(false);
    }
  };

  const handleDeleteComment = async (commentId: number) => {
    if (!window.confirm("Bạn có chắc chắn muốn xóa bình luận này?")) return;

    setDeletingComment(commentId);
    try {
      console.log("Attempting to delete comment:", commentId);
      const response = await apiService.deleteComment(commentId.toString());
      console.log("Delete response:", response);

      if (response.success) {
        setComments((prev) => {
          // Check if it's a main comment or a reply
          const isMainComment = prev.some((c) => c.id === commentId);

          if (isMainComment) {
            // Remove main comment
            return prev.filter((c) => c.id !== commentId);
          } else {
            // Remove reply from parent comment
            return prev.map((comment) => ({
              ...comment,
              replies:
                comment.replies?.filter((reply) => reply.id !== commentId) ||
                [],
              replies_count: Math.max(0, (comment.replies_count || 0) - 1),
            }));
          }
        });
        toast.success("Đã xóa bình luận!");
        setShowDropdown(null); // Close dropdown after successful delete
      } else {
        console.error("Delete failed:", response);
        toast.error("Không thể xóa bình luận. Vui lòng thử lại.");
      }
    } catch (error: any) {
      console.error("Error deleting comment:", error);
      if (error.response?.status === 403) {
        toast.error("Bạn không có quyền xóa bình luận này");
      } else if (error.response?.status === 404) {
        toast.error("Bình luận không tồn tại");
      } else {
        toast.error("Có lỗi xảy ra khi xóa bình luận");
      }
    } finally {
      setDeletingComment(null);
    }
  };

  const startEdit = (comment: Comment) => {
    setEditingComment(comment.id);
    setEditContent(comment.content);
    setShowDropdown(null);
  };

  const cancelEdit = () => {
    setEditingComment(null);
    setEditContent("");
  };

  const startReply = (commentId: number) => {
    setReplyingTo(commentId);
    setReplyContent("");
  };

  const cancelReply = () => {
    setReplyingTo(null);
    setReplyContent("");
  };

  const formatTimeAgo = (date: string) => {
    return formatDistanceToNow(new Date(date), {
      addSuffix: true,
      locale: vi,
    });
  };

  if (loading) {
    return (
      <div className="flex justify-center py-4">
        <LoadingSpinner size="md" />
      </div>
    );
  }

  return (
    <div className="border-t border-gray-200">
      {/* Header with close button */}
      {onClose && (
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold">Bình luận</h3>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded-full"
          >
            <XMarkIcon className="w-6 h-6 text-gray-400" />
          </button>
        </div>
      )}
      {/* Comments List */}
      <div className="max-h-80 overflow-y-auto relative z-50">
        {comments.map((comment) => (
          <div key={comment.id} className="p-4 hover:bg-gray-50">
            <div className="flex space-x-3">
              <img
                src={
                  comment.user.avatar ||
                  "https://api-private.atlassian.com/users/8b3597e8a7d1d8f2ed7f58bcab1946b8/avatar"
                }
                alt={comment.user.username}
                className="w-8 h-8 rounded-full object-cover"
              />
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <span className="font-semibold text-sm text-gray-900 mr-2">
                      {comment.user.username}
                    </span>
                    {editingComment === comment.id ? (
                      <form
                        onSubmit={(e) => handleEditSubmit(e, comment.id)}
                        className="mt-1"
                      >
                        <div className="flex items-center space-x-2">
                          <input
                            type="text"
                            value={editContent}
                            onChange={(e) => setEditContent(e.target.value)}
                            className="flex-1 text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:border-blue-500"
                            autoFocus
                          />
                          <button
                            type="submit"
                            disabled={submitting || !editContent.trim()}
                            className="text-xs font-semibold text-blue-600 hover:text-blue-700 disabled:opacity-50"
                          >
                            Lưu
                          </button>
                          <button
                            type="button"
                            onClick={cancelEdit}
                            className="text-xs font-semibold text-gray-500 hover:text-gray-700"
                          >
                            Hủy
                          </button>
                        </div>
                      </form>
                    ) : (
                      <span className="text-sm text-gray-900">
                        {comment.content}
                      </span>
                    )}
                  </div>
                  {user &&
                    user.user &&
                    (user.user.id === comment.user.id ||
                      user.user.id === postOwnerId) && (
                      <div
                        className="relative dropdown-container"
                        style={{ zIndex: "1000" }}
                      >
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            setShowDropdown(
                              showDropdown === comment.id ? null : comment.id
                            );
                          }}
                          className="p-1 hover:bg-gray-100 rounded-full ml-2"
                        >
                          <EllipsisHorizontalIcon className="w-4 h-4 text-gray-400" />
                        </button>
                        {showDropdown === comment.id && (
                          <div className="absolute right-0 mt-1 w-32 bg-white border border-gray-200 rounded-md shadow-lg z-50">
                            {user &&
                              user.user &&
                              user.user.id === comment.user.id && (
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    startEdit(comment);
                                  }}
                                  className="flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                >
                                  <PencilIcon className="w-4 h-4 mr-2" />
                                  Chỉnh sửa
                                </button>
                              )}
                            {user &&
                              user.user &&
                              (user.user.id === comment.user.id ||
                                user.user.id === postOwnerId) && (
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleDeleteComment(comment.id);
                                  }}
                                  disabled={deletingComment === comment.id}
                                  className="flex items-center w-full px-3 py-2 text-sm text-red-600 hover:bg-gray-100 disabled:opacity-50"
                                >
                                  <TrashIcon className="w-4 h-4 mr-2" />
                                  {deletingComment === comment.id
                                    ? "Đang xóa..."
                                    : "Xóa"}
                                </button>
                              )}
                          </div>
                        )}
                      </div>
                    )}
                </div>

                {editingComment !== comment.id && (
                  <div className="flex items-center space-x-4 mt-2">
                    <p className="text-xs text-gray-400">
                      {formatTimeAgo(comment.created_at)}
                    </p>
                    {comment.likes_count > 0 && (
                      <p className="text-xs text-gray-400">
                        {comment.likes_count} lượt thích
                      </p>
                    )}
                    <button
                      onClick={() => startReply(comment.id)}
                      className="text-xs text-gray-500 font-semibold hover:text-gray-700"
                    >
                      Trả lời
                    </button>
                  </div>
                )}

                {/* Reply Form */}
                {replyingTo === comment.id && (
                  <form
                    onSubmit={(e) => handleReplySubmit(e, comment.id)}
                    className="mt-3"
                  >
                    <div className="flex items-center space-x-2">
                      <input
                        type="text"
                        value={replyContent}
                        onChange={(e) => setReplyContent(e.target.value)}
                        placeholder={`Trả lời ${comment.user.username}...`}
                        className="flex-1 text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:border-blue-500"
                        autoFocus
                      />
                      <button
                        type="submit"
                        disabled={submitting || !replyContent.trim()}
                        className="text-xs font-semibold text-blue-600 hover:text-blue-700 disabled:opacity-50"
                      >
                        Gửi
                      </button>
                      <button
                        type="button"
                        onClick={cancelReply}
                        className="text-xs font-semibold text-gray-500 hover:text-gray-700"
                      >
                        <XMarkIcon className="w-4 h-4" />
                      </button>
                    </div>
                  </form>
                )}

                {/* Show Replies */}
                {comment.replies && comment.replies.length > 0 && (
                  <div className="ml-6 mt-3 space-y-3">
                    {comment.replies.map((reply) => (
                      <div key={reply.id} className="flex space-x-2">
                        <img
                          src={
                            reply.user.avatar ||
                            "https://api-private.atlassian.com/users/8b3597e8a7d1d8f2ed7f58bcab1946b8/avatar"
                          }
                          alt={reply.user.username}
                          className="w-6 h-6 rounded-full object-cover"
                        />
                        <div className="flex-1">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <span className="font-semibold text-xs text-gray-900 mr-2">
                                {reply.user.username}
                              </span>
                              {editingComment === reply.id ? (
                                <form
                                  onSubmit={(e) =>
                                    handleEditSubmit(e, reply.id)
                                  }
                                  className="mt-1"
                                >
                                  <div className="flex items-center space-x-2">
                                    <input
                                      type="text"
                                      value={editContent}
                                      onChange={(e) =>
                                        setEditContent(e.target.value)
                                      }
                                      className="flex-1 text-xs border border-gray-300 rounded px-2 py-1 focus:outline-none focus:border-blue-500"
                                      autoFocus
                                    />
                                    <button
                                      type="submit"
                                      disabled={
                                        submitting || !editContent.trim()
                                      }
                                      className="text-xs font-semibold text-blue-600 hover:text-blue-700 disabled:opacity-50"
                                    >
                                      Lưu
                                    </button>
                                    <button
                                      type="button"
                                      onClick={cancelEdit}
                                      className="text-xs font-semibold text-gray-500 hover:text-gray-700"
                                    >
                                      Hủy
                                    </button>
                                  </div>
                                </form>
                              ) : (
                                <span className="text-xs text-gray-900">
                                  {reply.content}
                                </span>
                              )}
                            </div>
                            {user &&
                              user.user &&
                              (user.user.id === reply.user.id ||
                                user.user.id === postOwnerId) && (
                                <div className="relative dropdown-container">
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      setShowDropdown(
                                        showDropdown === reply.id
                                          ? null
                                          : reply.id
                                      );
                                    }}
                                    className="p-1 hover:bg-gray-100 rounded-full ml-1"
                                  >
                                    <EllipsisHorizontalIcon className="w-3 h-3 text-gray-400" />
                                  </button>
                                  {showDropdown === reply.id && (
                                    <div className="absolute right-0 mt-1 w-32 bg-white border border-gray-200 rounded-md shadow-lg z-50">
                                      {user &&
                                        user.user &&
                                        user.user.id === reply.user.id && (
                                          <button
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              startEdit(reply);
                                            }}
                                            className="flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                          >
                                            <PencilIcon className="w-4 h-4 mr-2" />
                                            Chỉnh sửa
                                          </button>
                                        )}
                                      {user &&
                                        user.user &&
                                        (user.user.id === reply.user.id ||
                                          user.user.id === postOwnerId) && (
                                          <button
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              handleDeleteComment(reply.id);
                                            }}
                                            disabled={
                                              deletingComment === reply.id
                                            }
                                            className="flex items-center w-full px-3 py-2 text-sm text-red-600 hover:bg-gray-100 disabled:opacity-50"
                                          >
                                            <TrashIcon className="w-4 h-4 mr-2" />
                                            {deletingComment === reply.id
                                              ? "Đang xóa..."
                                              : "Xóa"}
                                          </button>
                                        )}
                                    </div>
                                  )}
                                </div>
                              )}
                          </div>
                          {editingComment !== reply.id && (
                            <p className="text-xs text-gray-400 mt-1">
                              {formatTimeAgo(reply.created_at)}
                            </p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Add Comment */}
      <div className="border-t border-gray-200 p-4">
        <form
          onSubmit={handleSubmitComment}
          className="flex items-center space-x-3"
        >
          <button type="button" className="hover:opacity-70 transition-opacity">
            <FaceSmileIcon className="w-6 h-6 text-gray-400" />
          </button>

          <input
            type="text"
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
            placeholder="Thêm bình luận..."
            className="flex-1 text-sm placeholder-gray-500 border-none outline-none bg-transparent"
            disabled={submitting}
          />

          {newComment.trim() && (
            <button
              type="submit"
              disabled={submitting}
              className="text-sm font-semibold text-instagram-blue hover:text-blue-700 disabled:opacity-50"
            >
              {submitting ? <LoadingSpinner size="sm" /> : "Đăng"}
            </button>
          )}
        </form>
      </div>
    </div>
  );
};

export default CommentSection;
