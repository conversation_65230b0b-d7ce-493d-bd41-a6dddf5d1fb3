<?php

namespace App\Http\Controllers;

use App\Models\Notification;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class NotificationController extends Controller
{
    /**
     * Get user's notifications
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();
        $filter = $request->get('filter', 'all'); // all, unread
        $perPage = $request->get('per_page', 20);

        $query = $user->notifications()->orderBy('created_at', 'desc');

        if ($filter === 'unread') {
            $query->unread();
        }

        $notifications = $query->paginate($perPage);

        // Load related data
        $notifications->getCollection()->transform(function ($notification) {
            $data = $notification->data;
            
            // Add actor information
            if (isset($data['actor_id'])) {
                $actor = User::find($data['actor_id']);
                if ($actor) {
                    $notification->actor = [
                        'id' => $actor->id,
                        'username' => $actor->username,
                        'full_name' => $actor->full_name,
                        'avatar' => $actor->avatar,
                        'is_verified' => $actor->is_verified ?? false,
                    ];
                }
            }

            // Add post information if exists
            if (isset($data['post_id'])) {
                $post = \App\Models\Post::find($data['post_id']);
                if ($post) {
                    $notification->post = [
                        'id' => $post->id,
                        'media_url' => $post->media_url,
                        'media_type' => $post->media_type,
                    ];
                }
            }

            // Add comment information if exists
            if (isset($data['comment_id'])) {
                $comment = \App\Models\Comment::find($data['comment_id']);
                if ($comment) {
                    $notification->comment = [
                        'id' => $comment->id,
                        'content' => $comment->content,
                    ];
                }
            }

            return $notification;
        });

        return response()->json($notifications);
    }

    /**
     * Mark notification as read
     */
    public function markAsRead(Request $request, $notificationId): JsonResponse
    {
        $user = $request->user();
        
        $notification = $user->notifications()->findOrFail($notificationId);
        $notification->markAsRead();

        return response()->json([
            'success' => true,
            'message' => 'Notification marked as read'
        ]);
    }

    /**
     * Mark all notifications as read
     */
    public function markAllAsRead(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $user->notifications()->unread()->update(['is_read' => true]);

        return response()->json([
            'success' => true,
            'message' => 'All notifications marked as read'
        ]);
    }

    /**
     * Get unread notifications count
     */
    public function getUnreadCount(Request $request): JsonResponse
    {
        $user = $request->user();
        $count = $user->notifications()->unread()->count();

        return response()->json([
            'unread_count' => $count
        ]);
    }

    /**
     * Delete notification
     */
    public function destroy(Request $request, $notificationId): JsonResponse
    {
        $user = $request->user();
        
        $notification = $user->notifications()->findOrFail($notificationId);
        $notification->delete();

        return response()->json([
            'success' => true,
            'message' => 'Notification deleted'
        ]);
    }
}