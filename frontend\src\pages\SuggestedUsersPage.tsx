import React, { useState, useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";

import LoadingSpinner from "../components/ui/LoadingSpinner";
import {
  ChevronLeftIcon,
  UserPlusIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import { toast } from "react-hot-toast";
import { usersService } from "../services/users";
import type { User } from "../types";

interface SuggestedUser extends User {
  mutual_followers_count: number;
  mutual_followers?: Array<{
    id: string;
    username: string;
    full_name: string;
  }>;
  suggestion_reason?: string;
}

const SuggestedUsersPage: React.FC = () => {
  const navigate = useNavigate();
  const [suggestedUsers, setSuggestedUsers] = useState<SuggestedUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [followingUsers, setFollowingUsers] = useState<Set<string>>(new Set());
  const [dismissedUsers, setDismissedUsers] = useState<Set<string>>(new Set());

  const fetchSuggestedUsers = useCallback(async () => {
    try {
      setLoading(true);
      const users = await usersService.getSuggestedUsers(20);
      setSuggestedUsers(users as SuggestedUser[]);
    } catch (error) {
      console.error("Error fetching suggested users:", error);
      toast.error("Không thể tải danh sách đề xuất");
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchSuggestedUsers();
  }, [fetchSuggestedUsers]);

  const handleFollow = async (userId: number) => {
    try {
      // Optimistic update
      setFollowingUsers((prev) => new Set([...prev, userId.toString()]));

      // Find the user to get username
      const user = suggestedUsers.find(u => u.id === userId);
      if (!user) {
        throw new Error('User not found');
      }

      await usersService.toggleFollowByUsername(user.username);
      toast.success("Đã theo dõi");
    } catch (error) {
      // Revert optimistic update
      setFollowingUsers((prev) => {
        const newSet = new Set(prev);
        newSet.delete(userId.toString());
        return newSet;
      });

      console.error("Error following user:", error);
      toast.error("Không thể theo dõi người dùng");
    }
  };

  const handleDismiss = (userId: number) => {
    setDismissedUsers((prev) => new Set([...prev, userId.toString()]));
    toast.success("Đã ẩn đề xuất");
  };

  const formatCount = (count: number): string => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  const getMutualFollowersText = (user: SuggestedUser): string => {
    if (!user.mutual_followers_count || user.mutual_followers_count === 0) {
      return "";
    }

    if (user.mutual_followers_count === 1) {
      return `Được theo dõi bởi ${
        user.mutual_followers?.[0]?.username || "bạn bè"
      }`;
    }

    if (user.mutual_followers_count === 2) {
      return `Được theo dõi bởi ${
        user.mutual_followers?.[0]?.username || "bạn bè"
      } và ${user.mutual_followers?.[1]?.username || "1 người khác"}`;
    }

    return `Được theo dõi bởi ${
      user.mutual_followers?.[0]?.username || "bạn bè"
    } và ${user.mutual_followers_count - 1} người khác`;
  };

  const visibleUsers = suggestedUsers.filter(
    (user) => !dismissedUsers.has(user.id.toString())
  );

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-4 py-3 sticky top-0 z-10">
        <div className="flex items-center space-x-4 max-w-2xl mx-auto">
          <button
            onClick={() => navigate(-1)}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <ChevronLeftIcon className="w-6 h-6" />
          </button>
          <h1 className="text-xl font-semibold">Đề xuất cho bạn</h1>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-2xl mx-auto">
        {visibleUsers.length === 0 ? (
          <div className="bg-white p-8 text-center">
            <UserPlusIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Không có đề xuất nào
            </h3>
            <p className="text-gray-500">
              Hãy thử tìm kiếm những người bạn biết hoặc khám phá nội dung mới
            </p>
          </div>
        ) : (
          <div className="bg-white divide-y divide-gray-100">
            {visibleUsers.map((suggestedUser) => (
              <div key={suggestedUser.id} className="p-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3 flex-1">
                    <button
                      onClick={() => navigate(`/${suggestedUser.username}`)}
                      className="flex-shrink-0"
                    >
                      <img
                        src={suggestedUser.avatar || "https://api-private.atlassian.com/users/8b3597e8a7d1d8f2ed7f58bcab1946b8/avatar"}
                        alt={suggestedUser.username}
                        className="w-12 h-12 rounded-full object-cover"
                      />
                    </button>

                    <div className="flex-1 min-w-0">
                      <button
                        onClick={() => navigate(`/${suggestedUser.username}`)}
                        className="text-left"
                      >
                        <div className="flex items-center space-x-1">
                          <span className="font-semibold text-gray-900 truncate">
                            {suggestedUser.username}
                          </span>
                          {suggestedUser.is_verified && (
                            <span className="text-blue-500 flex-shrink-0">
                              ✓
                            </span>
                          )}
                        </div>
                        <div className="text-sm text-gray-500 truncate">
                          {suggestedUser.full_name}
                        </div>
                        <div className="text-xs text-gray-400 mt-1">
                          {formatCount(suggestedUser.followers_count || 0)} người
                          theo dõi
                        </div>

                        {/* Mutual followers */}
                        {(suggestedUser.mutual_followers_count || 0) > 0 && (
                          <div className="text-xs text-gray-500 mt-1">
                            {getMutualFollowersText(suggestedUser)}
                          </div>
                        )}

                        {/* Suggestion reason */}
                        {suggestedUser.suggestion_reason && (
                          <div className="text-xs text-blue-500 mt-1">
                            {suggestedUser.suggestion_reason}
                          </div>
                        )}
                      </button>
                    </div>
                  </div>

                  {/* Action buttons */}
                  <div className="flex items-center space-x-2 ml-3">
                    <button
                      onClick={() => handleDismiss(suggestedUser.id)}
                      className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                      title="Ẩn đề xuất"
                    >
                      <XMarkIcon className="w-5 h-5 text-gray-400" />
                    </button>

                    <button
                      onClick={() => handleFollow(suggestedUser.id)}
                      disabled={followingUsers.has(suggestedUser.id.toString())}
                      className="px-4 py-1.5 bg-blue-500 text-white rounded-md text-sm font-medium hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      {followingUsers.has(suggestedUser.id.toString())
                        ? "Đang theo dõi"
                        : "Theo dõi"}
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Footer */}
        <div className="bg-white mt-1 p-6 text-center">
          <div className="text-sm text-gray-500 space-y-2">
            <p>
              Đề xuất dựa trên hoạt động của bạn và những người bạn theo dõi
            </p>
            <div className="flex justify-center space-x-4">
              <button
                onClick={() => navigate("/explore")}
                className="text-blue-500 hover:underline"
              >
                Khám phá thêm
              </button>
              <button
                onClick={() => navigate("/search")}
                className="text-blue-500 hover:underline"
              >
                Tìm kiếm bạn bè
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SuggestedUsersPage;
