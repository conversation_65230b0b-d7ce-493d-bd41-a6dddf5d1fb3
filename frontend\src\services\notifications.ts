import { apiService } from './api';
import type { Notification } from '../types';

export interface NotificationResponse {
  data: Notification[];
  current_page: number;
  last_page: number;
  per_page: number;
  total: number;
}

export class NotificationsService {
  // Get user notifications
  async getNotifications(page: number = 1, filter?: 'unread'): Promise<NotificationResponse> {
    const params: any = { page };
    if (filter) {
      params.filter = filter;
    }
    const response = await apiService.get<NotificationResponse>('/notifications', { params });
    return response;
  }

  // Mark notification as read
  async markAsRead(notificationId: number): Promise<{ success: boolean }> {
    const response = await apiService.patch<{ success: boolean }>(`/notifications/${notificationId}/read`);
    return response.data;
  }

  // Mark all notifications as read
  async markAllAsRead(): Promise<{ success: boolean }> {
    const response = await apiService.patch<{ success: boolean }>('/notifications/mark-all-read');
    return response.data;
  }

  // Get unread notifications count
  async getUnreadCount(): Promise<{ unread_count: number }> {
    const response = await apiService.get<{ unread_count: number }>('/notifications/unread-count');
    return response.unread_count;
  }

  // Delete notification
  async deleteNotification(notificationId: number): Promise<{ success: boolean }> {
    const response = await apiService.delete<{ success: boolean }>(`/notifications/${notificationId}`);
    return response.data;
  }
}

export const notificationsService = new NotificationsService();