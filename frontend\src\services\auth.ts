import { apiService } from './api';
import type {
  RegisterData,
  AuthResponse,
  User,
  ApiResponse,
} from '../types';

export class AuthService {
  async login(credentials: { login: string; password: string }): Promise<{ user: User; token: string }> {
    const response = await apiService.post('/login', credentials);

    if (response.data.token) {
      localStorage.setItem('token', response.data.token);
      localStorage.setItem('user', JSON.stringify(response.data.user));
    }

    return response.data;
  }

  async register(userData: RegisterData): Promise<{ user: User; token: string }> {
    const response = await apiService.post('/register', userData);

    if (response.data.token) {
      localStorage.setItem('token', response.data.token);
      localStorage.setItem('user', JSON.stringify(response.data.user));
    }

    return response.data;
  }

  async logout(): Promise<void> {
    try {
      await apiService.post('/logout');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      apiService.removeAuthToken();
      localStorage.removeItem('user');
    }
  }

  async getCurrentUser(): Promise<User> {
    const response = await apiService.get<User>('/user');
    localStorage.setItem('user', JSON.stringify(response.data));
    return response.data;
  }

  async forgotPassword(email: string): Promise<ApiResponse<{ message: string }>> {
    return await apiService.post('/forgot-password', { email });
  }

  async resetPassword(data: {
    email: string;
    token: string;
    password: string;
    password_confirmation: string;
  }): Promise<ApiResponse<{ message: string }>> {
    return await apiService.post('/reset-password', data);
  }

  async changePassword(data: {
    current_password: string;
    new_password: string;
    new_password_confirmation: string;
  }): Promise<ApiResponse<{ message: string }>> {
    return await apiService.post('/auth/change-password', data);
  }

  async verifyEmail(token: string): Promise<ApiResponse<{ message: string }>> {
    return await apiService.post('/auth/verify-email', { token });
  }

  async resendVerificationEmail(): Promise<ApiResponse<{ message: string }>> {
    return await apiService.post('/auth/resend-verification');
  }

  // Check if user is authenticated
  isAuthenticated(): boolean {
    return !!apiService.getAuthToken();
  }

  // Get current user from localStorage
  getCurrentUserFromStorage(): User | null {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  }

  // Refresh token
  async refreshToken(): Promise<AuthResponse> {
    const response = await apiService.post<AuthResponse>('/auth/refresh');

    if (response.data.token) {
      apiService.setAuthToken(response.data.token);
      localStorage.setItem('user', JSON.stringify(response.data.user));
    }

    return response.data;
  }

  // Get auth token
  getAuthToken(): string | null {
    return apiService.getAuthToken();
  }

  // Remove auth token
  removeAuthToken(): void {
    apiService.removeAuthToken();
  }
}

export const authService = new AuthService();
export default authService;