<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test User Types</title>
</head>
<body>
    <h1>Testing User Types from LocalStorage</h1>
    <div id="output"></div>

    <script>
        function log(message) {
            console.log(message);
            document.getElementById('output').innerHTML += message + '<br>';
        }

        // Test user data
        const testUser = {
            id: 13,
            username: "duongnh2",
            full_name: "<PERSON><PERSON><PERSON><PERSON>"
        };

        log('=== TESTING USER TYPES ===');
        log('Original user:');
        log(`ID: ${testUser.id} (type: ${typeof testUser.id})`);

        // Simulate localStorage storage
        localStorage.setItem('test_user', JSON.stringify(testUser));
        
        // Retrieve from localStorage
        const retrievedUser = JSON.parse(localStorage.getItem('test_user'));
        log('Retrieved user:');
        log(`ID: ${retrievedUser.id} (type: ${typeof retrievedUser.id})`);

        // Test message data
        const testMessage = {
            id: 17,
            user_id: 13,
            content: "Test message"
        };

        log('\\nOriginal message:');
        log(`Message ID: ${testMessage.id} (type: ${typeof testMessage.id})`);
        log(`User ID: ${testMessage.user_id} (type: ${typeof testMessage.user_id})`);

        // Simulate API response
        const apiResponse = JSON.stringify(testMessage);
        const parsedMessage = JSON.parse(apiResponse);
        
        log('\\nParsed message:');
        log(`Message ID: ${parsedMessage.id} (type: ${typeof parsedMessage.id})`);
        log(`User ID: ${parsedMessage.user_id} (type: ${typeof parsedMessage.user_id})`);

        // Test comparison
        log('\\n=== COMPARISON TESTS ===');
        log(`retrievedUser.id === parsedMessage.user_id: ${retrievedUser.id === parsedMessage.user_id}`);
        log(`retrievedUser.id == parsedMessage.user_id: ${retrievedUser.id == parsedMessage.user_id}`);
        log(`Number(retrievedUser.id) === Number(parsedMessage.user_id): ${Number(retrievedUser.id) === Number(parsedMessage.user_id)}`);

        // Test with string IDs (potential issue)
        const testUserString = {
            id: "13",
            username: "duongnh2"
        };
        
        const testMessageString = {
            id: "17",
            user_id: "13"
        };

        log('\\n=== STRING ID TESTS ===');
        log(`String user ID: ${testUserString.id} (type: ${typeof testUserString.id})`);
        log(`String message user_id: ${testMessageString.user_id} (type: ${typeof testMessageString.user_id})`);
        log(`String comparison: ${testUserString.id === testMessageString.user_id}`);
        log(`Mixed comparison (string user, number message): ${"13" === 13}`);
        log(`Mixed comparison (number user, string message): ${13 === "13"}`);
    </script>
</body>
</html>
