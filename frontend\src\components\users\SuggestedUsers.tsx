import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import type { User } from "../../types";

import { useAuth } from "../../contexts/AuthContext";
import LoadingSpinner from "../ui/LoadingSpinner";
import toast from "react-hot-toast";
import { usersService } from "../../services/users";

const SuggestedUsers: React.FC = () => {
  const { user } = useAuth();
  const [suggestedUsers, setSuggestedUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [followingUsers, setFollowingUsers] = useState<Set<number>>(new Set());
  console.log("user", user);
  useEffect(() => {
    fetchSuggestedUsers();
  }, []);

  const fetchSuggestedUsers = async () => {
    try {
      const users = await usersService.getSuggestedUsers(5);
      console.log(user);
      setSuggestedUsers(users);
    } catch (error) {
      console.error("Error fetching suggested users:", error);
      // toast.error("Không thể tải danh sách gợi ý");
    } finally {
      setLoading(false);
    }
  };

  const handleFollow = async (userId: number) => {
    try {
      setFollowingUsers((prev) => new Set([...prev, userId]));

      const result = await usersService.toggleFollow(userId);

      // Update the user's following status
      setSuggestedUsers((prev) =>
        prev.map((u) =>
          u.id === userId
            ? {
                ...u,
                is_following: result.is_following,
                followers_count: result.followers_count,
              }
            : u
        )
      );

      toast.success(result.is_following ? "Đã theo dõi!" : "Đã bỏ theo dõi!");
    } catch (error) {
      setFollowingUsers((prev) => {
        const newSet = new Set(prev);
        newSet.delete(userId);
        return newSet;
      });
      console.error("Error following user:", error);
      toast.error("Có lỗi xảy ra");
    }
  };

  const handleUnfollow = async (userId: number) => {
    try {
      const result = await usersService.toggleFollow(userId);

      // Update the user's following status
      setSuggestedUsers((prev) =>
        prev.map((u) =>
          u.id === userId
            ? {
                ...u,
                is_following: result.is_following,
                followers_count: result.followers_count,
              }
            : u
        )
      );

      toast.success(result.is_following ? "Đã theo dõi!" : "Đã bỏ theo dõi!");
    } catch (error) {
      console.error("Error unfollowing user:", error);
      toast.error("Có lỗi xảy ra");
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg p-4">
        <div className="flex justify-center">
          <LoadingSpinner size="md" />
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg">
      {/* Current User */}
      {user && (
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <Link to={`/${user.user?.username || user.username}`}>
              <img
                src={
                  user.user?.avatar ||
                  user?.avatar ||
                  "https://api-private.atlassian.com/users/8b3597e8a7d1d8f2ed7f58bcab1946b8/avatar"
                }
                alt={user.user?.username || user.username || ""}
                className="w-10 h-10 rounded-full object-cover"
              />
            </Link>
            <div>
              <Link
                to={`/${user.user?.username || user.username || ""}`}
                className="font-semibold text-sm text-gray-900 hover:underline block"
              >
                {user.user?.username || user.username || ""}
              </Link>
              <p className="text-xs text-gray-500">
                {user.user?.full_name || user.full_name}
              </p>
            </div>
          </div>
          <Link
            to="/accounts/edit"
            className="text-xs text-instagram-blue font-semibold hover:underline"
          >
            Chuyển đổi
          </Link>
        </div>
      )}

      {/* Suggested Users */}
      {suggestedUsers.length > 0 && (
        <div className="p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-semibold text-gray-500">
              Gợi ý cho bạn
            </h3>
            <Link
              to="/explore/people"
              className="text-xs text-gray-900 font-semibold hover:underline"
            >
              Xem tất cả
            </Link>
          </div>
          <div className="space-y-3">
            {suggestedUsers.map((suggestedUser) => (
              <div
                key={suggestedUser.id}
                className="flex items-center justify-between"
              >
                <div className="flex items-center space-x-3">
                  <Link to={`/${suggestedUser.username}`}>
                    <img
                      src={
                        suggestedUser.avatar ||
                        "https://api-private.atlassian.com/users/8b3597e8a7d1d8f2ed7f58bcab1946b8/avatar"
                      }
                      alt={suggestedUser.username}
                      className="w-8 h-8 rounded-full object-cover"
                    />
                  </Link>
                  <div>
                    <Link
                      to={`/${suggestedUser.username}`}
                      className="font-semibold text-sm text-gray-900 hover:underline block"
                    >
                      {suggestedUser.username}
                    </Link>
                    <p className="text-xs text-gray-500">
                      {suggestedUser.full_name}
                    </p>
                  </div>
                </div>
                <button
                  onClick={() =>
                    suggestedUser.is_following
                      ? handleUnfollow(suggestedUser.id)
                      : handleFollow(suggestedUser.id)
                  }
                  disabled={followingUsers.has(suggestedUser.id)}
                  className="text-xs text-instagram-blue font-semibold hover:underline disabled:opacity-50"
                >
                  {followingUsers.has(suggestedUser.id)
                    ? "Đang xử lý..."
                    : suggestedUser.is_following
                    ? "Bỏ theo dõi"
                    : "Theo dõi"}
                </button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default SuggestedUsers;
