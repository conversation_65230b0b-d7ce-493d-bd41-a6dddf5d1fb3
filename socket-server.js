const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');

const app = express();
const server = http.createServer(app);

// Configure CORS for Socket.IO
const io = socketIo(server, {
  cors: {
    origin: ["http://localhost:5173", "http://localhost:3000"],
    methods: ["GET", "POST"],
    credentials: true
  }
});

app.use(cors());
app.use(express.json());

// Store connected users
const connectedUsers = new Map();
const userSockets = new Map();

// Middleware to authenticate socket connections
io.use((socket, next) => {
  const token = socket.handshake.auth.token;
  const userId = socket.handshake.auth.userId;

  if (token) {
    // In a real app, you'd verify the JWT token here
    // For now, we'll accept any token and use the provided userId
    socket.userId = userId || null;
    socket.token = token;
    next();
  } else {
    next(new Error('Authentication error'));
  }
});

io.on('connection', (socket) => {
  console.log(`User connected: ${socket.id}`);

  // Store user socket mapping
  if (socket.userId) {
    userSockets.set(socket.userId, socket.id);
    connectedUsers.set(socket.id, {
      userId: socket.userId,
      socketId: socket.id,
      connectedAt: new Date()
    });
  }

  // Handle post like events
  socket.on('post_liked', (data) => {
    console.log('Post liked:', data);
    // Broadcast to all connected clients except sender
    socket.broadcast.emit('post_like_updated', {
      post_id: data.post_id,
      user_id: data.user_id,
      is_liked: data.is_liked,
      likes_count: data.likes_count,
      timestamp: new Date().toISOString()
    });
  });

  // Handle post comment events
  socket.on('post_commented', (data) => {
    console.log('Post commented:', data);
    socket.broadcast.emit('post_comment_added', {
      post_id: data.post_id,
      comment: data.comment,
      timestamp: new Date().toISOString()
    });
  });

  // Handle post share events
  socket.on('post_shared', (data) => {
    console.log('Post shared:', data);
    socket.broadcast.emit('post_share_updated', {
      post_id: data.post_id,
      shared_by_user_id: data.shared_by_user_id,
      shared_by_user: data.shared_by_user,
      shares_count: data.shares_count,
      timestamp: new Date().toISOString()
    });
  });

  // Handle user follow events
  socket.on('user_followed', (data) => {
    console.log('User followed:', data);
    const targetSocketId = userSockets.get(data.target_user_id);
    if (targetSocketId) {
      io.to(targetSocketId).emit('follower_added', {
        follower_id: data.follower_id,
        follower: data.follower,
        timestamp: new Date().toISOString()
      });
    }
  });

  // Handle new post events
  socket.on('new_post_created', (data) => {
    console.log('New post created:', data);
    socket.broadcast.emit('new_post_available', {
      post: data.post,
      user: data.user,
      timestamp: new Date().toISOString()
    });
  });

  // Handle typing indicators for comments
  socket.on('typing_comment', (data) => {
    socket.broadcast.emit('user_typing_comment', {
      post_id: data.post_id,
      user_id: data.user_id,
      user_name: data.user_name,
      is_typing: data.is_typing
    });
  });

  // Handle user online status
  socket.on('update_status', (data) => {
    const user = connectedUsers.get(socket.id);
    if (user) {
      user.isOnline = data.is_online;
      user.lastSeen = new Date();

      // Broadcast user status to all connected clients
      socket.broadcast.emit('user_online', {
        user_id: user.userId,
        is_online: data.is_online,
        last_seen: user.lastSeen.toISOString()
      });
    }
  });

  // Handle messages (existing functionality)
  socket.on('join_conversation', (data) => {
    socket.join(`conversation_${data.conversation_id}`);
    console.log(`User ${socket.userId} joined conversation ${data.conversation_id}`);
  });

  socket.on('leave_conversation', (data) => {
    socket.leave(`conversation_${data.conversation_id}`);
    console.log(`User ${socket.userId} left conversation ${data.conversation_id}`);
  });

  // Handle direct message sending (for testing)
  socket.on('send_message', (data) => {
    console.log('Direct message received:', data);
    // Broadcast to conversation room
    socket.to(`conversation_${data.conversation_id}`).emit('new_message', {
      ...data,
      timestamp: new Date().toISOString()
    });
  });

  socket.on('typing', (data) => {
    // Get user info from connected users
    const user = connectedUsers.get(socket.id);
    if (user) {
      socket.to(`conversation_${data.conversation_id}`).emit('user_typing', {
        conversation_id: data.conversation_id,
        user: {
          id: user.userId,
          username: user.username || `User${user.userId}`,
          name: user.name || `User ${user.userId}`
        },
        is_typing: data.is_typing
      });
    }
  });

  // Handle disconnection
  socket.on('disconnect', (reason) => {
    console.log(`User disconnected: ${socket.id}, reason: ${reason}`);

    const user = connectedUsers.get(socket.id);
    if (user) {
      // Remove from connected users
      userSockets.delete(user.userId);
      connectedUsers.delete(socket.id);

      // Broadcast user offline status
      socket.broadcast.emit('user_online', {
        user_id: user.userId,
        is_online: false,
        last_seen: new Date().toISOString()
      });
    }
  });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    connectedUsers: connectedUsers.size,
    timestamp: new Date().toISOString()
  });
});

// API endpoint to trigger events from backend
app.post('/api/emit', (req, res) => {
  const { event, data, room } = req.body;

  if (!event || !data) {
    return res.status(400).json({ error: 'Event and data are required' });
  }

  console.log(`Emitting event: ${event} to room: ${room || 'global'}`);
  console.log('Event data:', data);

  if (room) {
    // Check if we need to exclude a specific user
    if (data.exclude_user_id) {
      const excludeUserId = data.exclude_user_id;
      const excludeSocketId = userSockets.get(excludeUserId);

      console.log(`Excluding user ${excludeUserId} (socket: ${excludeSocketId}) from broadcast`);

      // Remove exclude_user_id from data before sending
      const cleanData = { ...data };
      delete cleanData.exclude_user_id;

      // Emit to room but exclude the sender
      if (excludeSocketId) {
        io.to(room).except(excludeSocketId).emit(event, cleanData);
      } else {
        io.to(room).emit(event, cleanData);
      }
    } else {
      io.to(room).emit(event, data);
    }
    res.json({ success: true, message: `Event ${event} emitted to room ${room}` });
  } else {
    io.emit(event, data);
    res.json({ success: true, message: `Event ${event} emitted globally` });
  }
});

const PORT = process.env.PORT || 3001;
server.listen(PORT, () => {
  console.log(`Socket.IO server running on port ${PORT}`);
  console.log(`Health check available at http://localhost:${PORT}/health`);
});