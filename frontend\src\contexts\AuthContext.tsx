import React, { createContext, useContext, useEffect, useState } from "react";
import type { ReactNode } from "react";
import type { User, LoginCredentials, RegisterData } from "../types";
import { authService } from "../services/auth";
import { socketService } from "../services/socket";
import toast from "react-hot-toast";

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => Promise<void>;
  updateUser: (user: User) => void;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!user;

  // Initialize auth state
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const token = authService.getAuthToken();
        if (token) {
          // Try to get current user from API
          const currentUser = await authService.getCurrentUser();
          setUser(currentUser);

          // Connect to socket
          socketService.connect(token, currentUser.id);
        } else {
          // Check if user data exists in localStorage
          const storedUser = authService.getCurrentUserFromStorage();
          if (storedUser) {
            // Clear invalid stored data
            localStorage.removeItem("user");
          }
        }
      } catch (error) {
        console.error("Auth initialization error:", error);
        // Clear invalid auth data
        authService.removeAuthToken();
        localStorage.removeItem("user");
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  // Setup socket event listeners
  useEffect(() => {
    if (isAuthenticated) {
      // Listen for user updates via socket
      const handleUserUpdate = (updatedUser: User) => {
        setUser(updatedUser);
        localStorage.setItem("user", JSON.stringify(updatedUser));
      };

      socketService.on("user_updated", handleUserUpdate);

      return () => {
        socketService.off("user_updated", handleUserUpdate);
      };
    }
  }, [isAuthenticated, user]);

  const login = async (credentials: LoginCredentials): Promise<void> => {
    try {
      setIsLoading(true);
      const authResponse = await authService.login(credentials);
      setUser(authResponse.user);

      // Connect to socket
      socketService.connect(authResponse.token, authResponse.user.id);
    } catch (error: unknown) {
      // Don't show toast here, let the calling component handle it
      // This prevents duplicate error messages
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (userData: RegisterData) => {
    try {
      setIsLoading(true);
      const registerData: RegisterData = {
        ...userData,
        full_name: userData.full_name ?? "", // Gán "" nếu full_name là undefined hoặc null
      };
      const authResponse = await authService.register(registerData);
      setUser(authResponse.user);

      // Connect to socket
      socketService.connect(authResponse.token, authResponse.user.id);

      toast.success("Đăng ký thành công!");
    } catch (error: unknown) {
      // Don't show toast here, let the calling component handle it
      // This prevents duplicate error messages
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    try {
      setIsLoading(true);
      await authService.logout();
      setUser(null);

      // Disconnect socket
      socketService.disconnect();

      toast.success("Đăng xuất thành công!");
    } catch (error: unknown) {
      console.error("Logout error:", error);
      // Force logout even if API call fails
      setUser(null);
      authService.removeAuthToken();
      localStorage.removeItem("user");
      socketService.disconnect();
    } finally {
      setIsLoading(false);
    }
  };

  const updateUser = (updatedUser: User): void => {
    setUser(updatedUser);
    localStorage.setItem("user", JSON.stringify(updatedUser));
  };

  const refreshUser = async (): Promise<void> => {
    try {
      if (authService.isAuthenticated()) {
        const currentUser = await authService.getCurrentUser();
        setUser(currentUser);
      }
    } catch (error) {
      console.error("Error refreshing user:", error);
      // If refresh fails, user might need to login again
      await logout();
    }
  };

  // Handle browser tab visibility for socket connection
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (isAuthenticated) {
        if (document.hidden) {
          socketService.updateOnlineStatus(false);
        } else {
          socketService.updateOnlineStatus(true);
          // Reconnect if needed
          const token = authService.getAuthToken();
          if (token && !socketService.connected && user) {
            socketService.connect(token, user.id);
          }
        }
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [isAuthenticated, user]);

  // Handle page unload
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (isAuthenticated) {
        socketService.updateOnlineStatus(false);
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);

    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, [isAuthenticated]);

  const value: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    login,
    register,
    logout,
    updateUser,
    refreshUser,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

export default AuthContext;
