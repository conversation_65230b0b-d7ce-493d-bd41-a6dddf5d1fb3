import { useState, useEffect, useCallback } from 'react';
import type { Post, PostFormData, PaginatedResponse, Comment, User } from '../types';
import { isApiError } from '../types';
import { postsService } from '../services/posts';
import toast from 'react-hot-toast';
import { socketService } from '../services/socket';

interface UsePostsOptions {
  userId?: number;
  username?: string;
  hashtag?: string;
  type?: 'feed' | 'user' | 'userByUsername' | 'explore' | 'saved' | 'hashtag';
  limit?: number;
}

export const usePosts = (options: UsePostsOptions = {}) => {
  const [posts, setPosts] = useState<Post[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [error, setError] = useState<string | null>(null);

  const { userId, username, hashtag, type = 'feed', limit = 10 } = options;

  const fetchPosts = useCallback(async (page: number = 1, reset: boolean = false) => {
    try {
      if (page === 1) {
        setIsLoading(true);
      } else {
        setIsLoadingMore(true);
      }
      setError(null);

      let response: PaginatedResponse<Post>;

      switch (type) {
        case 'feed':
          response = await postsService.getFeedPosts(page, limit);
          break;
        case 'user':
          if (!userId) throw new Error('User ID is required for user posts');
          response = await postsService.getUserPosts(userId, page, limit);
          break;
        case 'userByUsername':
          if (!username) throw new Error('Username is required for user posts by username');
          response = await postsService.getUserPostsByUsername(username, page, limit);
          break;
        case 'explore':
          response = await postsService.getExplorePosts(page, limit);
          break;
        case 'saved':
          response = await postsService.getSavedPosts(page, limit);
          break;
        case 'hashtag':
          if (!hashtag) throw new Error('Hashtag is required for hashtag posts');
          response = await postsService.getPostsByHashtag(hashtag, page);
          break;
        default:
          throw new Error('Invalid post type');
      }

      if (reset) {
        setPosts(response.data);
        setCurrentPage(1);
      } else {
        setPosts(prev => [...prev, ...response.data]);
      }

      setHasMore(response.current_page < response.last_page);
      setCurrentPage(response.current_page);
    } catch (error: unknown) {
      const message = isApiError(error) ? error.response?.data?.message || error.message : error instanceof Error ? error.message : 'Failed to fetch posts';
      setError(message);
      toast.error(message);
    } finally {
      setIsLoading(false);
      setIsLoadingMore(false);
    }
  }, [type, userId, username, hashtag, limit]);

  const loadMore = useCallback(() => {
    if (!isLoadingMore && hasMore) {
      fetchPosts(currentPage + 1);
    }
  }, [fetchPosts, currentPage, isLoadingMore, hasMore]);

  const refresh = useCallback(() => {
    setCurrentPage(1);
    setHasMore(true);
    fetchPosts(1, true);
  }, [fetchPosts]);

  const createPost = useCallback(async (data: PostFormData, onProgress?: (progress: number) => void): Promise<Post> => {
    try {
      const newPost = await postsService.createPost(data, onProgress);
      setPosts(prev => [newPost, ...prev]);
      toast.success('Bài đăng đã được tạo thành công!');
      return newPost;
    } catch (error: unknown) {
      const message = isApiError(error) ? error.response?.data?.message || 'Failed to create post' : 'Failed to create post';
      toast.error(message);
      throw error;
    }
  }, []);

  const updatePost = useCallback(async (postId: number, data: { caption?: string; location?: string }): Promise<void> => {
    try {
      const updatedPost = await postsService.updatePost(postId, data);
      setPosts(prev => prev.map(post => post.id === postId ? updatedPost : post));
      toast.success('Bài đăng đã được cập nhật!');
    } catch (error: unknown) {
      const message = isApiError(error) ? error.response?.data?.message || 'Failed to update post' : 'Failed to update post';
      toast.error(message);
      throw error;
    }
  }, []);

  const deletePost = useCallback(async (postId: number): Promise<void> => {
    try {
      await postsService.deletePost(postId);
      setPosts(prev => prev.filter(post => post.id !== postId));
      toast.success('Bài đăng đã được xóa!');
    } catch (error: unknown) {
      const message = isApiError(error) ? error.response?.data?.message || 'Failed to delete post' : 'Failed to delete post';
      toast.error(message);
      throw error;
    }
  }, []);

  const toggleLike = useCallback(async (postId: number): Promise<void> => {
    try {
      // Optimistic update
      setPosts(prev => prev.map(post => {
        if (post.id === postId) {
          return {
            ...post,
            is_liked: !post.is_liked,
            likes_count: post.is_liked ? post.likes_count - 1 : post.likes_count + 1,
          };
        }
        return post;
      }));

      const result = await postsService.toggleLike(postId);

      // Update with actual result
      setPosts(prev => prev.map(post => {
        if (post.id === postId) {
          return {
            ...post,
            is_liked: result.is_liked,
            likes_count: result.likes_count,
          };
        }
        return post;
      }));
    } catch (error: unknown) {
      // Revert optimistic update
      setPosts(prev => prev.map(post => {
        if (post.id === postId) {
          return {
            ...post,
            is_liked: !post.is_liked,
            likes_count: post.is_liked ? post.likes_count + 1 : post.likes_count - 1,
          };
        }
        return post;
      }));

      const message = isApiError(error) ? error.response?.data?.message || 'Failed to toggle like' : 'Failed to toggle like';
      toast.error(message);
    }
  }, []);

  const toggleSave = useCallback(async (postId: number): Promise<void> => {
    try {
      // Optimistic update
      setPosts(prev => prev.map(post => {
        if (post.id === postId) {
          return {
            ...post,
            is_saved: !post.is_saved,
          };
        }
        return post;
      }));

      const result = await postsService.toggleSave(postId);

      // Update with actual result
      setPosts(prev => prev.map(post => {
        if (post.id === postId) {
          return {
            ...post,
            is_saved: result.is_saved,
          };
        }
        return post;
      }));
    } catch (error: unknown) {
      // Revert optimistic update
      setPosts(prev => prev.map(post => {
        if (post.id === postId) {
          return {
            ...post,
            is_saved: !post.is_saved,
          };
        }
        return post;
      }));

      const message = isApiError(error) ? error.response?.data?.message || 'Failed to toggle save' : 'Failed to toggle save';
      toast.error(message);
    }
  }, []);

  // Socket event handlers
  useEffect(() => {
    const handlePostLikeUpdated = (data: {
      post_id: number;
      likes_count: number;
      is_liked: boolean;
    }) => {
      // Update the post in the current posts list
      setPosts(prev => prev.map(post => {
        if (post.id === data.post_id) {
          return {
            ...post,
            likes_count: data.likes_count
            // Note: We don't update is_liked here because that's specific to the current user
            // The user who liked/unliked will get the update through the API response
          };
        }
        return post;
      }));
    };

    const handleNewPostAvailable = (data: {
      message: string;
    }) => {
      // Refresh posts when new post is available
      if (type === 'feed') {
        fetchPosts(1, true);
      }
    };

    const handlePostCommentAdded = (data: {
      post_id: number;
      comment: Comment;
    }) => {
      // Update comments count for the post
      setPosts(prev => prev.map(post => {
        if (post.id === data.post_id) {
          return {
            ...post,
            comments_count: post.comments_count + 1
          };
        }
        return post;
      }));
    };

    const handlePostShareUpdated = (data: {
      post_id: number;
      shares_count: number;
    }) => {
      // Update shares count for the post
      setPosts(prev => prev.map(post => {
        if (post.id === data.post_id) {
          return {
            ...post,
            shares_count: data.shares_count
          };
        }
        return post;
      }));
    };

    // Register socket listeners
    socketService.on('post_like_updated', handlePostLikeUpdated);
    socketService.on('new_post_available', handleNewPostAvailable);
    socketService.on('post_comment_added', handlePostCommentAdded);
    socketService.on('post_share_updated', handlePostShareUpdated);

    // Cleanup
    return () => {
      socketService.off('post_like_updated', handlePostLikeUpdated);
      socketService.off('new_post_available', handleNewPostAvailable);
      socketService.off('post_comment_added', handlePostCommentAdded);
      socketService.off('post_share_updated', handlePostShareUpdated);
    };
  }, [type]);

  // Initial load
  useEffect(() => {
    fetchPosts(1, true);
  }, [fetchPosts]);

  return {
    posts,
    isLoading,
    isLoadingMore,
    hasMore,
    error,
    loadMore,
    refresh,
    createPost,
    updatePost,
    deletePost,
    toggleLike,
    toggleSave,
  };
};

export default usePosts;