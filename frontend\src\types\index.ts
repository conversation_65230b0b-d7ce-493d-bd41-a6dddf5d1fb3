// User types
export interface User {
  id: number;
  username: string;
  email: string;
  full_name: string;
  bio?: string;
  website?: string;
  phone?: string;
  avatar?: string;
  cover_image?: string;
  is_verified: boolean;
  is_private: boolean;
  followers_count?: number | undefined;
  following_count?: number | undefined;
  posts_count?: number | undefined;
  created_at: string;
  updated_at: string;
  user?: User;
  is_following?: boolean;
  follow_status?: 'following' | 'accepted' | 'pending' | 'not_following';
  unfollow?: () => void;
  follow?: () => void;
  gender?: 'male' | 'female' | 'other';
  phone_number?: string;

}

// Post types
export interface Post {
  id: number;
  user_id: number;
  user: User;
  caption?: string;
  location?: string;
  media: PostMedia[];
  likes_count: number;
  comments_count: number;
  is_liked: boolean;
  is_saved: boolean;
  created_at: string;
  updated_at: string;
}

export interface PostMedia {
  id: number;
  post_id: number;
  type: 'image' | 'video';
  file_url: string;
  thumbnail_url?: string;
  width?: number;
  height?: number;
  order: number;
}

// Comment types
export interface Comment {
  id: number;
  post_id: number;
  user_id: number;
  user: User;
  content: string;
  likes_count: number;
  is_liked: boolean;
  replies_count: number;
  parent_id?: number;
  replies?: Comment[];
  created_at: string;
  updated_at: string;
}

// Story types
export interface Story {
  id: number;
  user_id: number;
  user: User;
  media_url: string;
  media_type: 'image' | 'video';
  duration?: number;
  is_viewed: boolean;
  created_at: string;
  expires_at: string;
}

// Message types
export interface Conversation {
  id: number;
  type: 'direct' | 'group';
  name?: string;
  image?: string;
  participants: User[];
  last_message?: Message;
  last_message_at?: string;
  unread_count: number;
  other_participant?: User;
  created_at: string;
  updated_at: string;
}

export interface Message {
  id: number;
  conversation_id: number;
  user_id: number;
  sender_id: number;
  sender?: User;
  user?: User;
  type: 'text' | 'image' | 'video' | 'post_share';
  content?: string;
  file_path?: string;
  file_url?: string;
  metadata?: Record<string, unknown>;
  edited_at?: string;
  is_read?: boolean;
  created_at: string;
  updated_at: string;
  temp_id?: string; // For temporary messages before they're saved
}

export interface TempMessage extends Omit<Message, 'id'> {
  temp_id: string;
  id?: number;
}

export type MessageWithTemp = Message | TempMessage;

// Notification types
export interface Notification {
  id: number;
  user_id: number;
  type: 'like' | 'comment' | 'follow' | 'mention' | 'message';
  actor?: {
    id: number;
    username: string;
    full_name: string;
    avatar?: string;
    is_verified?: boolean;
  };
  post?: {
    id: number;
    media_url: string;
    media_type: 'image' | 'video';
  };
  comment?: {
    id: number;
    content: string;
  };
  data?: any; // Keep for backward compatibility
  is_read: boolean;
  created_at: string;
}

// Follow types
export interface Follow {
  id: number;
  follower_id: number;
  following_id: number;
  follower: User;
  following: User;
  created_at: string;
}

// API Response types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  status: 'success' | 'error';
}

export interface PaginatedResponse<T> {
  data: T[];
  current_page: number;
  last_page: number;
  per_page: number;
  total: number;
  from: number;
  to: number;
}

// Auth types
export interface LoginCredentials {
  login: string;
  password: string;
}

export interface RegisterData {
  username: string;
  email: string;
  name: string;
  full_name: string;
  password: string;
  password_confirmation: string;
  phone?: string;
}

export interface AuthResponse {
  user: User;
  token: string;
  expires_at: string;
}

// Form types
export interface PostFormData {
  caption?: string;
  location?: string;
  media: File[];
}

export interface ProfileFormData {
  full_name: string;
  username: string;
  bio: string;
  website: string;
  email: string;
  phone: string;
  gender: string;
}

export interface PasswordChangeData {
  current_password: string;
  new_password: string;
  new_password_confirmation: string;
}

// Socket types
export interface SocketMessage {
  type: 'message' | 'typing' | 'read' | 'online' | 'offline';
  data: unknown;
}

// Search types
export interface SearchResult {
  users: User[];
  posts: Post[];
  hashtags: string[];
}

// Upload types
export interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

export interface MediaFile {
  file: File;
  preview: string;
  type: 'image' | 'video';
  id: string;
}

// API Response types
export interface ApiResponse<T = unknown> {
  data: T;
  message?: string;
  success: boolean;
  errors?: Record<string, string[]>;
}

export interface PaginatedResponse<T = unknown> {
  data: T[];
  current_page: number;
  last_page: number;
  per_page: number;
  total: number;
  from: number;
  to: number;
  links: {
    first: string;
    last: string;
    prev: string | null;
    next: string | null;
  };
}

// Error types
export interface ApiError extends Error {
  response?: {
    data?: {
      message?: string;
    };
  };
}

export function isApiError(error: unknown): error is ApiError {
  return error instanceof Error && 'response' in error;
}