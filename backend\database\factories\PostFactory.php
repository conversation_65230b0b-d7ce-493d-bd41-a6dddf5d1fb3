<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\User;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Post>
 */
class PostFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'caption' => fake()->optional(0.8)->paragraph(3),
            'location' => fake()->optional(0.3)->city(),
            'comments_disabled' => fake()->boolean(10), // 10% chance of disabled comments
            'likes_disabled' => fake()->boolean(5), // 5% chance of disabled likes
            'likes_count' => 0,
            'comments_count' => 0,
            'shares_count' => 0,
            'created_at' => fake()->dateTimeBetween('-6 months', 'now'),
            'updated_at' => function (array $attributes) {
                return $attributes['created_at'];
            },
        ];
    }
}
