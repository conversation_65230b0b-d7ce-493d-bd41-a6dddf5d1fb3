# Instagram Clone

Một ứng dụng web clone Instagram với đầy đủ tính năng social media và chat realtime.

## Công nghệ sử dụng

### Frontend

- ReactJS 18
- Vite
- Tailwind CSS
- Axios
- React Router
- Socket.IO Client

### Backend

- Laravel 10
- MySQL 8
- Laravel Sanctum (Authentication)
- Laravel Echo + Socket.IO
- Image/Video Upload

### Realtime

- Socket.IO Server
- Laravel Echo

## Tính năng

### Xác thực người dùng

- [x] Đăng ký với email/username/password
- [x] Đăng nhập/Đăng xuất
- [x] Quên mật khẩu qua email
- [x] JWT Authentication

### <PERSON><PERSON> sơ cá nhân

- [x] Xem và chỉnh sửa profile
- [x] Upload avatar và ảnh bìa
- [x] Đổi mật khẩu
- [x] Quản lý thông tin cá nhân

### B<PERSON><PERSON> đ<PERSON> (Posts)

- [x] Đăng ảnh/video/album
- [x] Chỉnh sửa và xóa bài đăng
- [x] Like, comment, share
- [x] Hashtags

### News Feed

- [x] Hiển thị bài đăng từ người theo dõi
- [x] Tìm kiếm bài đăng, hashtag, người dùng
- [x] Infinite scroll

### Tương tác xã hội

- [x] Follow/Unfollow người dùng
- [x] Chat realtime (Direct Message)
- [x] Notifications realtime

## Cài đặt và chạy dự án

### Yêu cầu hệ thống

- Node.js 18+
- PHP 8.1+
- Composer
- MySQL 8+
- Redis (optional, for caching)

### 1. Clone repository

```bash
git clone <repository-url>
cd Instagram
```

### 2. Setup Backend (Laravel)

```bash
cd backend
composer install
cp .env.example .env
php artisan key:generate
```

### 3. Cấu hình database

Chỉnh sửa file `.env` trong thư mục backend:

```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=instagram_clone
DB_USERNAME=root
DB_PASSWORD=
```

### 4. Chạy migration và seeder

```bash
php artisan migrate
php artisan db:seed
php artisan storage:link
```

### 5. Setup Frontend (React)

```bash
cd ../frontend
npm install
```

### 6. Cấu hình environment frontend

Tạo file `.env` trong thư mục frontend:

```env
VITE_API_URL=http://localhost:8000/api
VITE_SOCKET_URL=http://localhost:3001
```

### 7. Chạy ứng dụng

#### Terminal 1 - Backend Laravel

```bash
cd backend
php artisan serve
```

#### Terminal 2 - Socket.IO Server

```bash
cd backend
node socket-server.js
```

#### Terminal 3 - Frontend React

```bash
cd frontend
npm run dev
```

### 8. Truy cập ứng dụng

- Frontend: http://localhost:5173
- Backend API: http://localhost:8000
- Socket Server: http://localhost:3001

## Cấu trúc dự án

```
Instagram/
├── backend/                 # Laravel API
│   ├── app/
│   │   ├── Http/Controllers/
│   │   ├── Models/
│   │   ├── Services/
│   │   └── ...
│   ├── database/
│   │   ├── migrations/
│   │   └── seeders/
│   ├── routes/
│   └── socket-server.js     # Socket.IO server
├── frontend/                # React + Vite
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   ├── hooks/
│   │   ├── services/
│   │   └── utils/
│   └── public/
└── README.md
```

## API Documentation

### Authentication

- POST `/api/register` - Đăng ký
- POST `/api/login` - Đăng nhập
- POST `/api/logout` - Đăng xuất
- POST `/api/forgot-password` - Quên mật khẩu

### Posts

- GET `/api/posts` - Lấy danh sách bài đăng
- POST `/api/posts` - Tạo bài đăng mới
- PUT `/api/posts/{id}` - Cập nhật bài đăng
- DELETE `/api/posts/{id}` - Xóa bài đăng

### Users

- GET `/api/users/{id}` - Lấy thông tin user
- PUT `/api/users/{id}` - Cập nhật profile
- POST `/api/users/{id}/follow` - Follow user

### Messages

- GET `/api/conversations` - Lấy danh sách cuộc trò chuyện
- GET `/api/conversations/{id}/messages` - Lấy tin nhắn
- POST `/api/messages` - Gửi tin nhắn

## Demo Account

```
Email: <EMAIL>
Password: password123
```

## Tính năng nâng cao

- [x] Image optimization và resize
- [x] Video upload và streaming
- [x] Push notifications
- [x] Dark/Light theme
- [x] Mobile responsive
- [x] SEO optimization
- [x] Performance optimization

## Đóng góp

1. Fork dự án
2. Tạo feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Mở Pull Request

## License

MIT License - xem file [LICENSE](LICENSE) để biết thêm chi tiết.
