@tailwind base;
@tailwind components;
@tailwind utilities;

/* Instagram-like global styles */
@layer base {
  * {
    box-sizing: border-box;
  }

  html {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  }

  body {
    margin: 0;
    padding: 0;
    background-color: #fafafa;
    color: #262626;
    font-size: 14px;
    line-height: 18px;
    overflow-x: hidden;
  }

  input,
  textarea {
    font-family: inherit;
  }

  button {
    font-family: inherit;
  }
}

@layer components {

  /* Instagram button styles */
  .btn-primary {
    @apply bg-instagram-blue text-white font-semibold py-2 px-4 rounded-md hover:bg-blue-600 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-secondary {
    @apply bg-transparent border border-instagram-border text-instagram-dark font-semibold py-2 px-4 rounded-md hover:bg-gray-50 transition-colors duration-200;
  }

  /* Instagram input styles */
  .input-field {
    @apply w-full px-3 py-2 border border-instagram-border rounded-md bg-instagram-light-gray text-sm focus:outline-none focus:border-gray-400 focus:bg-white transition-colors duration-200;
  }

  /* Animation for notifications panel */
  .animate-slide-in-left {
    animation: slideInLeft 0.3s ease-out;
  }

  @keyframes slideInLeft {
    from {
      transform: translateX(-100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  /* Instagram card styles */
  .card {
    @apply bg-white border border-instagram-border rounded-lg;
  }

  /* Instagram avatar styles */
  .avatar {
    @apply rounded-full object-cover;
  }

  /* Instagram story ring */
  .story-ring {
    @apply p-0.5 rounded-full bg-gradient-to-tr from-yellow-400 via-red-500 to-purple-500;
  }

  /* Instagram heart animation */
  .heart-animation {
    @apply transform transition-transform duration-150 active:scale-125;
  }

  /* Custom scrollbar */
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
}

@layer utilities {

  /* Hide scrollbar but keep functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Text truncation utilities */
  .text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .text-truncate-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}