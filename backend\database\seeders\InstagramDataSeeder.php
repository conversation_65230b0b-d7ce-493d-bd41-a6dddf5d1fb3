<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Post;
use App\Models\PostMedia;
use App\Models\Follow;
use App\Models\Like;
use App\Models\Comment;

class InstagramDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all users
        $users = User::all();

        if ($users->count() < 2) {
            $this->command->info('Not enough users to create sample data. Please run UserSeeder first.');
            return;
        }

        // Create posts for each user
        $users->each(function ($user) {
            // Each user creates 2-5 posts
            $postCount = rand(2, 5);

            for ($i = 0; $i < $postCount; $i++) {
                $post = Post::factory()->create([
                    'user_id' => $user->id,
                ]);

                // Each post has 1-3 media items
                $mediaCount = rand(1, 3);

                for ($j = 0; $j < $mediaCount; $j++) {
                    PostMedia::factory()->create([
                        'post_id' => $post->id,
                        'order' => $j + 1,
                    ]);
                }
            }
        });

        // Create some follows (users follow each other)
        $users->each(function ($user) use ($users) {
            $otherUsers = $users->where('id', '!=', $user->id);
            $followCount = rand(1, min(5, $otherUsers->count()));

            $otherUsers->random($followCount)->each(function ($followedUser) use ($user) {
                Follow::firstOrCreate([
                    'follower_id' => $user->id,
                    'following_id' => $followedUser->id,
                ]);
            });
        });

        // Create some likes
        $posts = Post::all();
        $users->each(function ($user) use ($posts) {
            $likeCount = rand(1, min(10, $posts->count()));

            $posts->random($likeCount)->each(function ($post) use ($user) {
                Like::firstOrCreate([
                    'user_id' => $user->id,
                    'likeable_id' => $post->id,
                    'likeable_type' => 'App\\Models\\Post',
                ]);
            });
        });

        // Create some comments
        $posts->each(function ($post) use ($users) {
            $commentCount = rand(0, 5);

            for ($i = 0; $i < $commentCount; $i++) {
                $comment = Comment::factory()->create([
                    'post_id' => $post->id,
                    'user_id' => $users->random()->id,
                ]);

                // Some comments get likes too
                if (rand(1, 100) <= 30) { // 30% chance
                    $likeCount = rand(1, 3);
                    $users->random($likeCount)->each(function ($user) use ($comment) {
                        Like::firstOrCreate([
                            'user_id' => $user->id,
                            'likeable_id' => $comment->id,
                            'likeable_type' => 'App\\Models\\Comment',
                        ]);
                    });
                }
            }
        });

        $this->command->info('Instagram sample data created successfully!');
    }
}
