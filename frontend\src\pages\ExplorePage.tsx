import React, { useState, useEffect } from "react";
import { usePosts } from "../hooks/usePosts";
import PostCard from "../components/posts/PostCard";
import ProfilePostCard from "../components/posts/ProfilePostCard";
import LoadingSpinner from "../components/ui/LoadingSpinner";
import { MagnifyingGlassIcon } from "@heroicons/react/24/outline";
import InfiniteScroll from "react-infinite-scroll-component";
import { usersService } from "../services/users";
import { postsService } from "../services/posts";
import { toast } from "react-hot-toast";
import type { User, Post } from "../types";

interface SearchResult {
  id: string;
  type: "user" | "hashtag" | "post";
  username?: string;
  full_name?: string;
  avatar?: string;
  followers_count?: number;
  hashtag?: string;
  posts_count?: number;
  caption?: string;
  media?: string;
  likes_count?: number;
  comments_count?: number;
}

const ExplorePage: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [searchLoading, setSearchLoading] = useState(false);
  const [showSearchResults, setShowSearchResults] = useState(false);

  const {
    posts,
    isLoading: loading,
    hasMore,
    refresh,
    loadMore,
  } = usePosts({ type: "explore" });

  useEffect(() => {
    refresh();
  }, [refresh]);

  useEffect(() => {
    if (searchQuery.trim()) {
      handleSearch();
    } else {
      setShowSearchResults(false);
      setSearchResults([]);
    }
  }, [searchQuery]);

  const handleSearch = async () => {
    if (!searchQuery.trim()) return;

    try {
      setSearchLoading(true);

      // Search users, hashtags, and posts in parallel
      const [usersResponse, hashtagsResponse, postsResponse] =
        await Promise.all([
          usersService.searchUsers(searchQuery).catch(() => ({ data: [] })),
          postsService.searchHashtags(searchQuery).catch(() => []),
          postsService.searchPosts(searchQuery).catch(() => ({ data: [] })),
        ]);

      const results: SearchResult[] = [];
      console.log("usersResponse", usersResponse);

      // Add users to results
      if (usersResponse && Array.isArray(usersResponse)) {
        usersResponse.forEach((user: User) => {
          results.push({
            id: user.id.toString(),
            type: "user",
            username: user.username,
            full_name: user.full_name,
            avatar: user.avatar,
            followers_count: user.followers_count || 0,
          });
        });
      }

      // Add hashtags to results
      if (Array.isArray(hashtagsResponse)) {
        hashtagsResponse.forEach(
          (hashtag: { name: string; posts_count: number }) => {
            results.push({
              id: hashtag.name,
              type: "hashtag",
              hashtag: hashtag.name,
              posts_count: hashtag.posts_count || 0,
            });
          }
        );
      }

      // Add posts to results
      if (postsResponse.data && Array.isArray(postsResponse.data)) {
        postsResponse.data.forEach((post: Post) => {
          results.push({
            id: post.id.toString(),
            type: "post",
            caption: post.caption,
            username: post.user?.username,
            avatar: post.user?.avatar,
            media: post.media?.[0]?.file_url,
            likes_count: post.likes_count || 0,
            comments_count: post.comments_count || 0,
          });
        });
      }

      setSearchResults(results);
      setShowSearchResults(true);
    } catch (error: unknown) {
      console.error("Error searching:", error);
      toast.error("Có lỗi xảy ra khi tìm kiếm");
    } finally {
      setSearchLoading(false);
    }
  };

  const formatCount = (count: number): string => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  const handleResultClick = (result: SearchResult) => {
    if (result.type === "user") {
      window.location.href = `/${result.username}`;
    } else if (result.type === "hashtag") {
      // In a real app, navigate to hashtag page
      setSearchQuery(`#${result.hashtag}`);
      setShowSearchResults(false);
      // You can add navigation to hashtag page here
    } else if (result.type === "post") {
      // Navigate to post detail page
      window.location.href = `/p/${result.id}`;
    }
    setShowSearchResults(false);
  };

  return (
    <div className="max-w-6xl mx-auto px-4 py-6">
      {/* Search Bar */}
      <div className="relative mb-8">
        <div className="relative">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Tìm kiếm"
            className="w-full pl-10 pr-4 py-3 bg-gray-100 border-none rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:bg-white transition-all"
          />
        </div>

        {/* Search Results Dropdown */}
        {showSearchResults && (
          <div className="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg mt-1 max-h-96 overflow-y-auto z-10">
            {searchLoading ? (
              <div className="flex justify-center py-4">
                <LoadingSpinner size="sm" />
              </div>
            ) : searchResults.length === 0 ? (
              <div className="px-4 py-3 text-gray-500 text-center">
                Không tìm thấy kết quả nào
              </div>
            ) : (
              <div className="py-2">
                {searchResults.map((result) => (
                  <div
                    key={result.id}
                    onClick={() => handleResultClick(result)}
                    className="flex items-center px-4 py-3 hover:bg-gray-50 cursor-pointer transition-colors"
                  >
                    {result.type === "user" ? (
                      <>
                        <img
                          src={result.avatar || "https://api-private.atlassian.com/users/8b3597e8a7d1d8f2ed7f58bcab1946b8/avatar"}
                          alt={result.username}
                          className="w-10 h-10 rounded-full object-cover mr-3"
                        />
                        <div className="flex-1">
                          <div className="font-semibold text-sm">
                            {result.username}
                          </div>
                          <div className="text-gray-500 text-xs">
                            {result.full_name} •{" "}
                            {formatCount(result.followers_count || 0)} người
                            theo dõi
                          </div>
                        </div>
                      </>
                    ) : result.type === "hashtag" ? (
                      <>
                        <div className="w-10 h-10 bg-gray-200 rounded-lg flex items-center justify-center mr-3">
                          <span className="text-lg font-bold text-gray-600">
                            #
                          </span>
                        </div>
                        <div className="flex-1">
                          <div className="font-semibold text-sm">
                            #{result.hashtag}
                          </div>
                          <div className="text-gray-500 text-xs">
                            {formatCount(result.posts_count || 0)} bài viết
                          </div>
                        </div>
                      </>
                    ) : (
                      <>
                        <div className="w-10 h-10 mr-3">
                          {result.media ? (
                            <img
                              src={result.media}
                              alt="Post"
                              className="w-10 h-10 rounded object-cover"
                            />
                          ) : (
                            <div className="w-10 h-10 bg-gray-200 rounded flex items-center justify-center">
                              <span className="text-gray-600 text-xs">📷</span>
                            </div>
                          )}
                        </div>
                        <div className="flex-1">
                          <div className="font-semibold text-sm">
                            @{result.username}
                          </div>
                          <div className="text-gray-700 text-xs line-clamp-2">
                            {result.caption || "Không có mô tả"}
                          </div>
                          <div className="text-gray-500 text-xs mt-1">
                            {formatCount(result.likes_count || 0)} lượt thích •{" "}
                            {formatCount(result.comments_count || 0)} bình luận
                          </div>
                        </div>
                      </>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Explore Content */}
      {!showSearchResults && (
        <div>
          <h2 className="text-xl font-semibold mb-6">Khám phá</h2>

          {loading && posts.length === 0 ? (
            <div className="flex justify-center py-8">
              <LoadingSpinner size="lg" />
            </div>
          ) : posts.length === 0 ? (
            <div className="text-center py-16">
              <div className="text-2xl font-light mb-4">
                Không có bài viết nào
              </div>
              <div className="text-gray-600">
                Hãy thử tìm kiếm hoặc theo dõi thêm người dùng.
              </div>
            </div>
          ) : (
            <InfiniteScroll
              dataLength={posts.length}
              next={loadMore}
              hasMore={hasMore}
              loader={
                <div className="flex justify-center py-4">
                  <LoadingSpinner size="md" />
                </div>
              }
              endMessage={
                <div className="text-center py-8 text-gray-500">
                  Bạn đã xem hết tất cả bài viết
                </div>
              }
            >
              <div className="grid grid-cols-3 gap-1 md:gap-4">
                {posts.map((post) => (
                  <ProfilePostCard
                    key={post.id}
                    post={post}
                    onLike={() => {
                      /* Handle like functionality */
                    }}
                    onSave={() => {
                      /* Handle save functionality */
                    }}
                  />
                ))}
              </div>
            </InfiniteScroll>
          )}
        </div>
      )}

      {/* Search Results Content */}
      {showSearchResults && searchQuery.startsWith("#") && (
        <div>
          <h2 className="text-xl font-semibold mb-6">
            #{searchQuery.slice(1)}
          </h2>

          {loading && posts.length === 0 ? (
            <div className="flex justify-center py-8">
              <LoadingSpinner size="lg" />
            </div>
          ) : posts.length === 0 ? (
            <div className="text-center py-16">
              <div className="text-2xl font-light mb-4">
                Không có bài viết nào
              </div>
              <div className="text-gray-600">
                Chưa có bài viết nào với hashtag này.
              </div>
            </div>
          ) : (
            <InfiniteScroll
              dataLength={posts.length}
              next={loadMore}
              hasMore={hasMore}
              loader={
                <div className="flex justify-center py-4">
                  <LoadingSpinner size="md" />
                </div>
              }
              endMessage={
                <div className="text-center py-8 text-gray-500">
                  Bạn đã xem hết tất cả bài viết
                </div>
              }
            >
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {posts.map((post) => (
                  <PostCard
                    key={post.id}
                    post={post}
                    onLike={() => {
                      /* Handle like functionality */
                    }}
                    onSave={() => {
                      /* Handle save functionality */
                    }}
                  />
                ))}
              </div>
            </InfiniteScroll>
          )}
        </div>
      )}
    </div>
  );
};

export default ExplorePage;
