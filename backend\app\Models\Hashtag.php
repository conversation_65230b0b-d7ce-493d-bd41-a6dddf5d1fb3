<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Hashtag extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'posts_count',
    ];

    protected $casts = [
        'posts_count' => 'integer',
    ];

    // Relationships
    public function posts()
    {
        return $this->belongsToMany(Post::class, 'post_hashtags');
    }

    // Accessors
    public function getFormattedNameAttribute()
    {
        return '#' . $this->name;
    }

    // Scopes
    public function scopePopular($query, $limit = 10)
    {
        return $query->orderBy('posts_count', 'desc')->limit($limit);
    }

    public function scopeSearch($query, $term)
    {
        return $query->where('name', 'like', '%' . $term . '%')
            ->orWhere('slug', 'like', '%' . Str::slug($term) . '%');
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($hashtag) {
            if (empty($hashtag->slug)) {
                $hashtag->slug = Str::slug($hashtag->name);
            }
        });
    }
}
