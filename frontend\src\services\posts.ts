import { apiService } from './api';
import type {
  Post,
  PostFormData,
  Comment,
  PaginatedResponse,
  ApiResponse,
  User,
} from '../types';

export class PostsService {
  // Get posts for feed
  async getFeedPosts(page: number = 1, limit: number = 10): Promise<PaginatedResponse<Post>> {
    return await apiService.getPaginated<Post>('/posts/feed', { page, limit });
  }

  // Get posts by user
  async getUserPosts(userId: number, page: number = 1, limit: number = 12): Promise<PaginatedResponse<Post>> {
    return await apiService.getPaginated<Post>(`/users/${userId}/posts`, { page, limit });
  }

  // Get posts by username
  async getUserPostsByUsername(username: string, page: number = 1, limit: number = 12): Promise<PaginatedResponse<Post>> {
    return await apiService.getPaginated<Post>(`/users/${username}/posts`, { page, limit });
  }

  // Get single post
  async getPost(postId: number): Promise<Post> {
    const response = await apiService.get<Post>(`/posts/${postId}`);
    return response.data;
  }

  // Create new post
  async createPost(data: PostFormData, onProgress?: (progress: number) => void): Promise<Post> {
    const formData = new FormData();

    if (data.caption) {
      formData.append('caption', data.caption);
    }

    if (data.location) {
      formData.append('location', data.location);
    }

    data.media.forEach((file, index) => {
      formData.append(`media[${index}]`, file);
    });

    const response = await apiService.upload<Post>('/posts', formData, onProgress);
    return response.data;
  }

  // Update post
  async updatePost(postId: number, data: { caption?: string; location?: string }): Promise<Post> {
    const response = await apiService.put<Post>(`/posts/${postId}`, data);
    return response.data;
  }

  // Delete post
  async deletePost(postId: number): Promise<void> {
    await apiService.delete(`/posts/${postId}`);
  }

  // Like/Unlike post
  async toggleLike(postId: number): Promise<{ is_liked: boolean; likes_count: number }> {
    const response = await apiService.post<{ is_liked: boolean; likes_count: number }>(`/posts/${postId}/like`);
    return response.data;
  }

  // Save/Unsave post
  async toggleSave(postId: number): Promise<{ is_saved: boolean }> {
    const response = await apiService.post<{ is_saved: boolean }>(`/posts/${postId}/save`);
    return response.data;
  }

  // Get post likes
  async getPostLikes(postId: number, page: number = 1): Promise<PaginatedResponse<User>> {
    return await apiService.getPaginated(`/posts/${postId}/likes`, { page });
  }

  // Get saved posts
  async getSavedPosts(page: number = 1, limit: number = 12): Promise<PaginatedResponse<Post>> {
    return await apiService.getPaginated<Post>('/posts/saved', { page, limit });
  }

  // Search posts
  async searchPosts(query: string, page: number = 1): Promise<PaginatedResponse<Post>> {
    return await apiService.getPaginated<Post>('/posts/search', { q: query, page });
  }

  // Get posts by hashtag
  async getPostsByHashtag(hashtag: string, page: number = 1): Promise<PaginatedResponse<Post>> {
    return await apiService.getPaginated<Post>(`/posts/hashtag/${hashtag}`, { page });
  }

  // Report post
  async reportPost(postId: number, reason: string): Promise<ApiResponse<{ success: boolean }>> {
    return await apiService.post(`/posts/${postId}/report`, { reason });
  }

  // Get explore posts
  async getExplorePosts(page: number = 1, limit: number = 21): Promise<PaginatedResponse<Post>> {
    return await apiService.getPaginated<Post>('/posts/explore', { page, limit });
  }

  // Search hashtags
  async searchHashtags(query: string): Promise<{ name: string; posts_count: number }[]> {
    const response = await apiService.get<{ name: string; posts_count: number }[]>('/hashtags/search', { q: query });
    return response.data;
  }
}

export class CommentsService {
  // Get post comments
  async getPostComments(postId: number, page: number = 1): Promise<PaginatedResponse<Comment>> {
    return await apiService.getPaginated<Comment>(`/posts/${postId}/comments`, { page });
  }

  // Create comment
  async createComment(postId: number, content: string, parentId?: number): Promise<Comment> {
    const response = await apiService.post<Comment>(`/posts/${postId}/comments`, {
      content,
      parent_id: parentId,
    });
    return response.data;
  }

  // Update comment
  async updateComment(commentId: number, content: string): Promise<Comment> {
    const response = await apiService.put<Comment>(`/comments/${commentId}`, { content });
    return response.data;
  }

  // Delete comment
  async deleteComment(commentId: number): Promise<void> {
    await apiService.delete(`/comments/${commentId}`);
  }

  // Like/Unlike comment
  async toggleCommentLike(commentId: number): Promise<{ is_liked: boolean; likes_count: number }> {
    const response = await apiService.post<{ is_liked: boolean; likes_count: number }>(`/comments/${commentId}/like`);
    return response.data;
  }

  // Get comment replies
  async getCommentReplies(commentId: number, page: number = 1): Promise<PaginatedResponse<Comment>> {
    return await apiService.getPaginated<Comment>(`/comments/${commentId}/replies`, { page });
  }

  // Report comment
  async reportComment(commentId: number, reason: string): Promise<ApiResponse<{ success: boolean }>> {
    return await apiService.post(`/comments/${commentId}/report`, { reason });
  }
}

export const postsService = new PostsService();
export const commentsService = new CommentsService();