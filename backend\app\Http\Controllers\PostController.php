<?php

namespace App\Http\Controllers;

use App\Models\Post;
use App\Models\PostMedia;
use App\Models\Hashtag;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class PostController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->user();

        // Get posts from followed users and own posts
        $followingIds = $user->following()->pluck('users.id')->toArray();
        $followingIds[] = $user->id; // Include own posts

        $posts = Post::whereIn('user_id', $followingIds)
            ->with(['user', 'media', 'hashtags'])
            ->withCount(['likes', 'comments'])
            ->latest()
            ->paginate(10);

        // Add user interaction data
        $posts->getCollection()->transform(function ($post) use ($user) {
            $post->is_liked = $post->isLikedBy($user);

            // Transform media to include full URLs
            $post->media->transform(function ($media) {
                $baseUrl = rtrim(config('app.url'), '/');
                
                // Debug logging
                \Log::info('DEBUG - baseUrl: ' . $baseUrl);
                \Log::info('DEBUG - Storage::url result: ' . Storage::url('test.jpg'));
                
                $media->file_url = $media->file_path ? $baseUrl . Storage::url($media->file_path) : null;
                $media->thumbnail_url = $media->thumbnail_path ? $baseUrl . Storage::url($media->thumbnail_path) : null;
                
                \Log::info('DEBUG - Final file_url: ' . $media->file_url);
                \Log::info('DEBUG - Final thumbnail_url: ' . $media->thumbnail_url);
                
                return $media;
            });

            return $post;
        });

        return response()->json([
            'success' => true,
            'data' => $posts
        ]);
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'caption' => 'nullable|string|max:2200',
            'location' => 'nullable|string|max:255',
            'media' => 'required|array|min:1|max:10',
            'media.*' => 'file|mimes:jpeg,png,jpg,gif,mp4,mov,avi|max:51200', // 50MB max
            'comments_disabled' => 'boolean',
            'likes_disabled' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        DB::beginTransaction();

        try {
            // Create post
            $post = Post::create([
                'user_id' => $request->user()->id,
                'caption' => $request->caption,
                'location' => $request->location,
                'comments_disabled' => $request->boolean('comments_disabled'),
                'likes_disabled' => $request->boolean('likes_disabled'),
            ]);

            // Process media files
            $mediaFiles = $request->file('media');
            foreach ($mediaFiles as $index => $file) {
                $mediaType = str_starts_with($file->getMimeType(), 'video/') ? 'video' : 'image';

                // Store file
                $filePath = $file->store('posts', 'public');

                // Get file metadata
                $metadata = [
                    'file_size' => $file->getSize(),
                    'mime_type' => $file->getMimeType(),
                    'original_name' => $file->getClientOriginalName(),
                ];

                // For images, get dimensions
                if ($mediaType === 'image') {
                    $imagePath = Storage::disk('public')->path($filePath);
                    $imageSize = getimagesize($imagePath);
                    if ($imageSize) {
                        $metadata['width'] = $imageSize[0];
                        $metadata['height'] = $imageSize[1];
                    }
                }

                // Create media record
                PostMedia::create([
                    'post_id' => $post->id,
                    'type' => $mediaType,
                    'file_path' => $filePath,
                    'order' => $index,
                    'metadata' => $metadata,
                ]);
            }

            DB::commit();

            $post->load(['user', 'media', 'hashtags']);

            return response()->json([
                'success' => true,
                'message' => 'Post created successfully',
                'data' => $post
            ], 201);
        } catch (\Exception $e) {
            DB::rollback();

            return response()->json([
                'success' => false,
                'message' => 'Failed to create post',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function show($id)
    {
        $post = Post::with(['user', 'media', 'hashtags', 'comments.user', 'comments.replies.user'])
            ->withCount(['likes', 'comments'])
            ->findOrFail($id);

        // Add user interaction data if authenticated
        if (auth()->check()) {
            $post->is_liked = $post->isLikedBy(auth()->user());
        }

        // Transform media to include full URLs
        $post->media->transform(function ($media) {
            $baseUrl = rtrim(config('app.url'), '/');
            
            $media->file_url = $media->file_path ? $baseUrl . Storage::url($media->file_path) : null;
            $media->thumbnail_url = $media->thumbnail_path ? $baseUrl . Storage::url($media->thumbnail_path) : null;
            return $media;
        });

        return response()->json([
            'success' => true,
            'data' => $post
        ]);
    }

    public function update(Request $request, $id)
    {
        $post = Post::findOrFail($id);

        // Check if user owns the post
        if ($post->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'caption' => 'nullable|string|max:2200',
            'location' => 'nullable|string|max:255',
            'comments_disabled' => 'boolean',
            'likes_disabled' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $post->update($request->only([
            'caption',
            'location',
            'comments_disabled',
            'likes_disabled'
        ]));

        $post->load(['user', 'media', 'hashtags']);

        return response()->json([
            'success' => true,
            'message' => 'Post updated successfully',
            'data' => $post
        ]);
    }

    public function destroy(Request $request, $id)
    {
        $post = Post::findOrFail($id);

        // Check if user owns the post
        if ($post->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        $post->delete();

        return response()->json([
            'success' => true,
            'message' => 'Post deleted successfully'
        ]);
    }

    public function like(Request $request, $id)
    {
        $post = Post::findOrFail($id);

        if ($post->likes_disabled) {
            return response()->json([
                'success' => false,
                'message' => 'Likes are disabled for this post'
            ], 400);
        }

        $isLiked = $post->toggleLike($request->user());
        $freshPost = $post->fresh();

        // Handle notifications
        if ($isLiked) {
            NotificationService::createLikeNotification($request->user(), $post);
        } else {
            NotificationService::removeLikeNotification($request->user(), $post);
        }

        // Emit socket event for realtime updates
        $this->emitSocketEvent('post_like_updated', [
            'post_id' => $post->id,
            'user_id' => $request->user()->id,
            'is_liked' => $isLiked,
            'likes_count' => $freshPost->likes_count,
            'timestamp' => now()->toISOString()
        ]);

        return response()->json([
            'success' => true,
            'message' => $isLiked ? 'Post liked' : 'Post unliked',
            'data' => [
                'is_liked' => $isLiked,
                'likes_count' => $freshPost->likes_count
            ]
        ]);
    }

    public function explore(Request $request)
    {
        $posts = Post::with(['user', 'media'])
            ->withCount(['likes', 'comments'])
            ->inRandomOrder()
            ->paginate(20);

        // Transform media to include full URLs
        $posts->getCollection()->transform(function ($post) {
            $post->media->transform(function ($media) {
                $baseUrl = rtrim(config('app.url'), '/');
                
                $media->file_url = $media->file_path ? $baseUrl . Storage::url($media->file_path) : null;
                $media->thumbnail_url = $media->thumbnail_path ? $baseUrl . Storage::url($media->thumbnail_path) : null;
                return $media;
            });
            return $post;
        });

        return response()->json([
            'success' => true,
            'data' => $posts
        ]);
    }

    public function search(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'q' => 'required|string|min:1',
            'type' => 'in:posts,hashtags,users',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $query = $request->q;
        $type = $request->type ?? 'posts';

        $results = [];

        switch ($type) {
            case 'posts':
                $results = Post::where('caption', 'like', "%{$query}%")
                    ->with(['user', 'media'])
                    ->withCount(['likes', 'comments'])
                    ->latest()
                    ->paginate(20);

                // Transform media to include full URLs
                $results->getCollection()->transform(function ($post) {
                    $post->media->transform(function ($media) {
                        $baseUrl = rtrim(config('app.url'), '/');
                        
                        $media->file_url = $media->file_path ? $baseUrl . Storage::url($media->file_path) : null;
                        $media->thumbnail_url = $media->thumbnail_path ? $baseUrl . Storage::url($media->thumbnail_path) : null;
                        return $media;
                    });
                    return $post;
                });
                break;

            case 'hashtags':
                $results = \App\Models\Hashtag::search($query)
                    ->with(['posts' => function ($q) {
                        $q->with('media')->latest()->take(9);
                    }])
                    ->paginate(20);

                // Transform media URLs for hashtag posts
                $results->getCollection()->transform(function ($hashtag) {
                    $hashtag->posts->transform(function ($post) {
                        $post->media->transform(function ($media) {
                            $baseUrl = rtrim(config('app.url'), '/');
                            
                            $media->file_url = $media->file_path ? $baseUrl . Storage::url($media->file_path) : null;
                            $media->thumbnail_url = $media->thumbnail_path ? $baseUrl . Storage::url($media->thumbnail_path) : null;
                            return $media;
                        });
                        return $post;
                    });
                    return $hashtag;
                });
                break;

            case 'users':
                $results = \App\Models\User::where('username', 'like', "%{$query}%")
                    ->orWhere('name', 'like', "%{$query}%")
                    ->paginate(20);
                break;
        }

        return response()->json([
            'success' => true,
            'data' => $results
        ]);
    }

    /**
     * Emit socket event to Socket.IO server
     */
    private function emitSocketEvent($event, $data)
    {
        try {
            $socketUrl = env('SOCKET_SERVER_URL', 'http://localhost:3001');
            Http::timeout(5)->post($socketUrl . '/api/emit', [
                'event' => $event,
                'data' => $data
            ]);
        } catch (\Exception $e) {
            // Log error but don't fail the request
            \Log::warning('Failed to emit socket event: ' . $e->getMessage());
        }
    }
}