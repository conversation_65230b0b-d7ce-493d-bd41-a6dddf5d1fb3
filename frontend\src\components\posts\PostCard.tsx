import React, { useState } from "react";
import { Link } from "react-router-dom";
import type { Post } from "../../types";
import {
  HeartIcon,
  ChatBubbleOvalLeftIcon,
  PaperAirplaneIcon,
  BookmarkIcon,
  EllipsisHorizontalIcon,
} from "@heroicons/react/24/outline";
import {
  HeartIcon as HeartIconSolid,
  BookmarkIcon as BookmarkIconSolid,
} from "@heroicons/react/24/solid";
import { formatDistanceToNow } from "date-fns";
import { vi } from "date-fns/locale";
import PostModal from "./PostModal";
import CommentSection from "./CommentSection";

interface PostCardProps {
  post: Post;
  onLike: () => void;
  onSave: () => void;
  showComments?: boolean;
}

const PostCard: React.FC<PostCardProps> = ({
  post,
  onLike,
  onSave,
  showComments = false,
}) => {
  const [showModal, setShowModal] = useState(false);
  const [showCommentsSection, setShowCommentsSection] = useState(showComments);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  const handleImageNavigation = (direction: "prev" | "next") => {
    if (direction === "prev" && currentImageIndex > 0) {
      setCurrentImageIndex(currentImageIndex - 1);
    } else if (
      direction === "next" &&
      currentImageIndex < post.media.length - 1
    ) {
      setCurrentImageIndex(currentImageIndex + 1);
    }
  };

  const formatTimeAgo = (date: string) => {
    return formatDistanceToNow(new Date(date), {
      addSuffix: true,
      locale: vi,
    });
  };

  return (
    <>
      <div
        className="bg-white border border-gray-200 rounded-lg overflow-hidden"
        style={{ marginBottom: "25px" }}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center space-x-3">
            <Link to={`/${post.user.username}`}>
              <img
                src={
                  post.user.avatar ||
                  "https://api-private.atlassian.com/users/8b3597e8a7d1d8f2ed7f58bcab1946b8/avatar"
                }
                alt={post.user.username}
                className="w-8 h-8 rounded-full object-cover"
              />
            </Link>
            <div>
              <Link
                to={`/${post.user.username}`}
                className="font-semibold text-sm text-gray-900 hover:underline"
              >
                {post.user.username}
              </Link>
              {post.location && (
                <p className="text-xs text-gray-500">{post.location}</p>
              )}
            </div>
          </div>
        </div>

        {/* Media */}
        <div className="relative">
          {post.media.length > 0 && (
            <>
              <img
                src={post.media[currentImageIndex]?.file_url}
                alt="Post media"
                className="w-full aspect-square object-cover cursor-pointer"
                onClick={() => setShowModal(true)}
              />
              {/* Image Navigation */}
              {post.media.length > 1 && (
                <>
                  {currentImageIndex > 0 && (
                    <button
                      onClick={() => handleImageNavigation("prev")}
                      className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white rounded-full p-1 hover:bg-opacity-70"
                    >
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M15 19l-7-7 7-7"
                        />
                      </svg>
                    </button>
                  )}

                  {currentImageIndex < post.media.length - 1 && (
                    <button
                      onClick={() => handleImageNavigation("next")}
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white rounded-full p-1 hover:bg-opacity-70"
                    >
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 5l7 7-7 7"
                        />
                      </svg>
                    </button>
                  )}

                  {/* Dots indicator */}
                  <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-1">
                    {post.media.map((_, index) => (
                      <div
                        key={index}
                        className={`w-2 h-2 rounded-full ${
                          index === currentImageIndex
                            ? "bg-white"
                            : "bg-white bg-opacity-50"
                        }`}
                      />
                    ))}
                  </div>
                </>
              )}
            </>
          )}
        </div>

        {/* Actions */}
        <div className="p-4">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-4">
              <button
                onClick={onLike}
                className="hover:opacity-70 transition-opacity"
              >
                {post.is_liked ? (
                  <HeartIconSolid className="w-6 h-6 text-red-500" />
                ) : (
                  <HeartIcon className="w-6 h-6 text-gray-900" />
                )}
              </button>

              <button
                onClick={() => setShowCommentsSection(!showCommentsSection)}
                className="hover:opacity-70 transition-opacity"
              >
                <ChatBubbleOvalLeftIcon className="w-6 h-6 text-gray-900" />
              </button>
            </div>
          </div>

          {/* Likes */}
          {post.likes_count > 0 && (
            <p className="font-semibold text-sm text-gray-900 mb-2">
              {post.likes_count} lượt thích
            </p>
          )}

          {/* Caption */}
          {post.caption && (
            <div className="mb-2">
              <span className="font-semibold text-sm text-gray-900 mr-2">
                {post.user.username}
              </span>
              <span className="text-sm text-gray-900">{post.caption}</span>
            </div>
          )}

          {/* Comments preview */}
          {post.comments_count > 0 && !showCommentsSection && (
            <button
              onClick={() => setShowCommentsSection(true)}
              className="text-sm text-gray-500 hover:underline mb-2 block"
            >
              Xem tất cả {post.comments_count} bình luận
            </button>
          )}

          {/* Time */}
          <p className="text-xs text-gray-400 uppercase">
            {formatTimeAgo(post.created_at)}
          </p>
        </div>

        {/* Comments Section */}
        {showCommentsSection && (
          <CommentSection
            postId={post.id}
            postOwnerId={post.user_id}
            onClose={() => setShowCommentsSection(false)}
          />
        )}
      </div>

      {/* Post Modal */}
      {showModal && (
        <PostModal
          post={post}
          onClose={() => setShowModal(false)}
          onLike={onLike}
          onSave={onSave}
        />
      )}
    </>
  );
};

export default PostCard;
