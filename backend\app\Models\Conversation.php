<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Conversation extends Model
{
    use HasFactory;

    protected $fillable = [
        'type',
        'name',
        'image',
        'last_message_at',
    ];

    protected $casts = [
        'last_message_at' => 'datetime',
    ];

    // Relationships
    public function participants()
    {
        return $this->belongsToMany(User::class, 'conversation_participants')
            ->withPivot(['joined_at', 'last_read_at', 'is_admin'])
            ->withTimestamps();
    }

    public function messages()
    {
        return $this->hasMany(Message::class)->orderBy('created_at', 'desc');
    }

    public function latestMessage()
    {
        return $this->hasOne(Message::class)->latest();
    }

    // Helper methods
    public function isDirect()
    {
        return $this->type === 'direct';
    }

    public function isGroup()
    {
        return $this->type === 'group';
    }

    public function addParticipant(User $user, $isAdmin = false)
    {
        return $this->participants()->attach($user->id, [
            'joined_at' => now(),
            'is_admin' => $isAdmin,
        ]);
    }

    public function removeParticipant(User $user)
    {
        return $this->participants()->detach($user->id);
    }

    public function hasParticipant(User $user)
    {
        return $this->participants()->where('user_id', $user->id)->exists();
    }

    public function getOtherParticipant(User $currentUser)
    {
        if (!$this->isDirect()) {
            return null;
        }

        return $this->participants()->where('user_id', '!=', $currentUser->id)->first();
    }

    public function markAsRead(User $user)
    {
        $this->participants()->updateExistingPivot($user->id, [
            'last_read_at' => now(),
        ]);
    }

    public function getUnreadCount(User $user)
    {
        $participant = $this->participants()->where('user_id', $user->id)->first();

        if (!$participant) {
            return 0;
        }

        $lastReadAt = $participant->pivot->last_read_at;

        return $this->messages()
            ->where('user_id', '!=', $user->id)
            ->when($lastReadAt, function ($query) use ($lastReadAt) {
                return $query->where('created_at', '>', $lastReadAt);
            })
            ->count();
    }

    public static function findOrCreateDirectConversation(User $user1, User $user2)
    {
        // Find existing direct conversation between these two users
        $conversation = self::where('type', 'direct')
            ->whereHas('participants', function ($query) use ($user1) {
                $query->where('user_id', $user1->id);
            })
            ->whereHas('participants', function ($query) use ($user2) {
                $query->where('user_id', $user2->id);
            })
            ->first();

        if (!$conversation) {
            // Create new conversation
            $conversation = self::create([
                'type' => 'direct',
            ]);

            $conversation->addParticipant($user1);
            $conversation->addParticipant($user2);
        }

        return $conversation;
    }
}
