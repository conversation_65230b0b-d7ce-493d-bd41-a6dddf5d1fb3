import React from "react";
import { Link, useLocation } from "react-router-dom";
import { useAuth } from "../../contexts/AuthContext";
import { useNotifications } from "../../hooks/useNotifications";
import {
  HomeIcon,
  MagnifyingGlassIcon,
  PlusIcon,
  ChatBubbleLeftIcon,
  HeartIcon,
  UserIcon,
} from "@heroicons/react/24/outline";
import {
  HomeIcon as HomeIconSolid,
  MagnifyingGlassIcon as SearchIconSolid,
  ChatBubbleLeftIcon as MessageIconSolid,
  HeartIcon as HeartIconSolid,
  UserIcon as UserIconSolid,
} from "@heroicons/react/24/solid";

const MobileBottomNav: React.FC = () => {
  const { user } = useAuth();
  const { unreadCount } = useNotifications();
  const location = useLocation();

  const menuItems = [
    {
      name: "Trang chủ",
      path: "/",
      icon: HomeIcon,
      activeIcon: HomeIconSolid,
    },
    {
      name: "<PERSON><PERSON><PERSON> kiế<PERSON>",
      path: "/explore",
      icon: MagnifyingGlassIcon,
      activeIcon: SearchIconSolid,
    },
    {
      name: "Tạ<PERSON>",
      path: "/create",
      icon: PlusIcon,
      activeIcon: PlusIcon,
    },
    {
      name: "Tin nhắn",
      path: "/messages",
      icon: ChatBubbleLeftIcon,
      activeIcon: MessageIconSolid,
    },
    {
      name: "Thông báo",
      path: "/notifications",
      icon: HeartIcon,
      activeIcon: HeartIconSolid,
      hasNotification: true,
    },
    {
      name: "Hồ sơ",
      path: `/${user?.username}`,
      icon: UserIcon,
      activeIcon: UserIconSolid,
    },
  ];

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50">
      <nav className="flex justify-around items-center py-2">
        {menuItems.map((item) => {
          const isActive = location.pathname === item.path;
          const Icon = isActive ? item.activeIcon : item.icon;
          const showBadge = item.hasNotification && unreadCount > 0;

          return (
            <Link
              key={item.name}
              to={item.path}
              className={`flex flex-col items-center justify-center p-2 rounded-lg transition-colors duration-200 ${
                isActive ? "text-gray-900" : "text-gray-500"
              }`}
            >
              <div className="relative">
                <Icon className="w-6 h-6" />
                {showBadge && (
                  <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full min-w-[16px] h-[16px] flex items-center justify-center px-1">
                    {unreadCount > 9 ? '9+' : unreadCount}
                  </div>
                )}
              </div>
            </Link>
          );
        })}
      </nav>
    </div>
  );
};

export default MobileBottomNav;
