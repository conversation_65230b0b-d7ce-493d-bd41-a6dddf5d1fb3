<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class PostMedia extends Model
{
    use HasFactory;

    protected $table = 'post_media';

    protected $fillable = [
        'post_id',
        'type',
        'file_path',
        'thumbnail_path',
        'order',
        'metadata',
    ];

    protected $casts = [
        'metadata' => 'array',
        'order' => 'integer',
    ];

    protected $appends = [
        'file_url',
        'thumbnail_url',
    ];

    // Relationships
    public function post()
    {
        return $this->belongsTo(Post::class);
    }

    // Accessors
    public function getFileUrlAttribute()
    {
        return $this->file_path ? rtrim(config('app.url'), '/') . Storage::url($this->file_path) : null;
    }

    public function getThumbnailUrlAttribute()
    {
        return $this->thumbnail_path ? rtrim(config('app.url'), '/') . Storage::url($this->thumbnail_path) : null;
    }

    public function getIsImageAttribute()
    {
        return $this->type === 'image';
    }

    public function getIsVideoAttribute()
    {
        return $this->type === 'video';
    }

    // Helper methods
    public function getWidth()
    {
        return $this->metadata['width'] ?? null;
    }

    public function getHeight()
    {
        return $this->metadata['height'] ?? null;
    }

    public function getDuration()
    {
        return $this->metadata['duration'] ?? null;
    }

    public function getFileSize()
    {
        return $this->metadata['file_size'] ?? null;
    }

    protected static function boot()
    {
        parent::boot();

        static::deleting(function ($media) {
            // Delete files when media record is deleted
            if ($media->file_path && Storage::exists($media->file_path)) {
                Storage::delete($media->file_path);
            }
            if ($media->thumbnail_path && Storage::exists($media->thumbnail_path)) {
                Storage::delete($media->thumbnail_path);
            }
        });
    }
}
