import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";
import { useNotifications } from "../hooks/useNotifications";
import { useSidebar } from "../contexts/SidebarContext";
import type { Notification } from "../types";
import {
  HeartIcon,
  ChatBubbleOvalLeftIcon,
  UserPlusIcon,
  ChevronLeftIcon,
  EllipsisHorizontalIcon,
  PhotoIcon,
  XMarkIcon,
  BellIcon,
} from "@heroicons/react/24/outline";
import { HeartIcon as HeartIconSolid } from "@heroicons/react/24/solid";
import { formatDistanceToNow } from "date-fns";
import { vi } from "date-fns/locale";
import LoadingSpinner from "../components/ui/LoadingSpinner";
import toast from "react-hot-toast";
import { notificationsService } from "../services/notifications";
import { usersService } from "../services/users";
import {
  <PERSON><PERSON><PERSON>,
  XMarkIcon as XMarkIconSmall,
} from "@heroicons/react/24/outline";

interface User {
  id: string;
  username: string;
  full_name: string;
  avatar?: string;
  is_verified?: boolean;
}

interface Post {
  id: string;
  media_url: string;
  media_type: "image" | "video";
}

interface Notification {
  id: number;
  type:
    | "like"
    | "comment"
    | "follow"
    | "follow_request"
    | "mention"
    | "post_tag";
  actor?: {
    id: number;
    username: string;
    full_name: string;
    avatar?: string;
    is_verified?: boolean;
  };
  post?: {
    id: number;
    media_url: string;
    media_type: "image" | "video";
  };
  comment?: {
    id: number;
    content: string;
  };
  created_at: string;
  is_read: boolean;
}

const NotificationsPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const {
    notifications,
    loading,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    refresh,
  } = useNotifications();
  const { setIsCollapsed } = useSidebar();
  const [filter, setFilter] = useState<"all" | "unread">("all");

  useEffect(() => {
    refresh();
  }, [refresh]);
  console.log("notifications", notifications);
  // Filter notifications based on selected filter
  const filteredNotifications = notifications;

  const handleNotificationClick = async (notification: Notification) => {
    // Navigate based on notification type
    if (notification.post?.id) {
      navigate(`/p/${notification.post.id}`);
    } else if (notification.actor?.username) {
      navigate(`/${notification.actor.username}`);
    }
  };

  const handleAcceptFollowRequest = async (
    notification: Notification,
    event: React.MouseEvent
  ) => {
    event.stopPropagation();
    if (!notification.actor?.id) return;

    try {
      console.log(
        "Starting accept follow request for actor ID:",
        notification.actor.id
      );

      // Get follow request ID first
      const followRequestResponse = await usersService.getFollowRequestId(
        notification.actor.id
      );
      console.log("Follow request response:", followRequestResponse);

      if (
        !followRequestResponse.success ||
        !followRequestResponse.follow_request_id
      ) {
        console.error(
          "Failed to get follow request ID:",
          followRequestResponse
        );
        toast.error("Không tìm thấy yêu cầu theo dõi");
        return;
      }

      console.log(
        "Accepting follow request with ID:",
        followRequestResponse.follow_request_id
      );
      const acceptResponse = await usersService.acceptFollowRequest(
        followRequestResponse.follow_request_id
      );
      console.log("Accept response:", acceptResponse);

      await markAsRead(notification.id);
      toast.success(
        `Đã chấp nhận yêu cầu theo dõi từ ${notification.actor.username}`
      );
      refresh();
    } catch (error) {
      console.error("Error accepting follow request:", error);
      console.error("Error details:", {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
        statusText: error.response?.statusText,
      });
    }
  };

  const handleRejectFollowRequest = async (
    notification: Notification,
    event: React.MouseEvent
  ) => {
    event.stopPropagation();
    if (!notification.actor?.id) return;

    try {
      console.log(
        "Starting reject follow request for actor ID:",
        notification.actor.id
      );

      // Get follow request ID first
      const followRequestResponse = await usersService.getFollowRequestId(
        notification.actor.id
      );
      console.log("Follow request response:", followRequestResponse);

      if (
        !followRequestResponse.success ||
        !followRequestResponse.follow_request_id
      ) {
        console.error(
          "Failed to get follow request ID:",
          followRequestResponse
        );
        toast.error("Không tìm thấy yêu cầu theo dõi");
        return;
      }

      console.log(
        "Rejecting follow request with ID:",
        followRequestResponse.follow_request_id
      );
      const rejectResponse = await usersService.rejectFollowRequest(
        followRequestResponse.follow_request_id
      );
      console.log("Reject response:", rejectResponse);

      await markAsRead(notification.id);
      toast.success(
        `Đã từ chối yêu cầu theo dõi từ ${notification.actor.username}`
      );
      refresh();
    } catch (error) {
      console.error("Error rejecting follow request:", error);
      console.error("Error details:", {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
        statusText: error.response?.statusText,
      });
      toast.error("Có lỗi xảy ra khi từ chối yêu cầu");
    }
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "like":
        return <HeartIcon className="w-3 h-3 text-red-500" />;
      case "follow":
        return <UserPlusIcon className="w-3 h-3 text-blue-500" />;
      case "follow_request":
        return <UserPlusIcon className="w-3 h-3 text-orange-500" />;
      case "comment":
      case "mention":
        return <ChatBubbleOvalLeftIcon className="w-3 h-3 text-green-500" />;
      case "post":
        return <PhotoIcon className="w-3 h-3 text-purple-500" />;
      default:
        return <BellIcon className="w-3 h-3 text-gray-500" />;
    }
  };

  const getNotificationText = (notification: Notification) => {
    switch (notification.type) {
      case "like":
        return "đã thích bài viết của bạn.";
      case "comment":
        return "đã bình luận về bài viết của bạn.";
      case "follow":
        return "đã bắt đầu theo dõi bạn.";
      case "follow_request":
        return "muốn theo dõi bạn.";
      case "mention":
        return "đã nhắc đến bạn trong một bình luận.";
      default:
        return "có hoạt động mới.";
    }
  };

  // const filteredNotifications = notifications.filter((notification) => {
  //   if (filter === "unread") {
  //     return !notification.is_read;
  //   }
  //   return true;
  // });
  console.log("test", notifications);
  const unreadCount = 0;

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  // Group notifications by time period
  const groupNotificationsByTime = (notifications: Notification[]) => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
    const thisWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    const thisMonth = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

    const groups = {
      today: [] as Notification[],
      yesterday: [] as Notification[],
      thisWeek: [] as Notification[],
      thisMonth: [] as Notification[],
      earlier: [] as Notification[],
    };

    notifications.forEach((notification) => {
      const notificationDate = new Date(notification.created_at);
      if (notificationDate >= today) {
        groups.today.push(notification);
      } else if (notificationDate >= yesterday) {
        groups.yesterday.push(notification);
      } else if (notificationDate >= thisWeek) {
        groups.thisWeek.push(notification);
      } else if (notificationDate >= thisMonth) {
        groups.thisMonth.push(notification);
      } else {
        groups.earlier.push(notification);
      }
    });

    return groups;
  };

  const groupedNotifications = groupNotificationsByTime(filteredNotifications);

  // NotificationItem component
  const NotificationItem = ({
    notification,
  }: {
    notification: Notification;
  }) => (
    <div
      onClick={() => handleNotificationClick(notification)}
      className="px-4 py-3 hover:bg-gray-50 cursor-pointer transition-colors"
    >
      <div className="flex items-center space-x-3">
        {/* User Avatar */}
        <div className="relative flex-shrink-0">
          <img
            src={
              notification.actor?.avatar ||
              "https://api-private.atlassian.com/users/8b3597e8a7d1d8f2ed7f58bcab1946b8/avatar"
            }
            alt={notification.actor?.username || "User"}
            className="w-11 h-11 rounded-full object-cover"
          />
          <div className="absolute -bottom-0.5 -right-0.5 bg-white rounded-full p-0.5">
            {getNotificationIcon(notification.type)}
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="text-sm text-gray-900">
                <span className="font-semibold">
                  {notification.actor?.username || "unknown"}
                </span>
                {notification.actor?.is_verified && (
                  <span className="ml-1 text-blue-500">✓</span>
                )}
                <span className="ml-1 font-normal">
                  {getNotificationText(notification)}
                </span>
                <span className="ml-1 text-gray-500 font-normal">
                  {formatDistanceToNow(new Date(notification.created_at), {
                    addSuffix: false,
                    locale: vi,
                  })
                    .replace("about ", "")
                    .replace("less than a minute", "1m")
                    .replace("minute", "m")
                    .replace("minutes", "m")
                    .replace("hour", "h")
                    .replace("hours", "h")
                    .replace("day", "d")
                    .replace("days", "d")
                    .replace("week", "w")
                    .replace("weeks", "w")}
                </span>
              </p>
            </div>

            {/* Post Thumbnail */}
            {notification.post && (
              <img
                src={notification.post.media_url}
                alt="Post"
                className="w-11 h-11 rounded object-cover ml-3 flex-shrink-0"
              />
            )}

            {/* Follow Request Actions */}
            {notification.type === "follow_request" && (
              <div className="flex space-x-2 ml-3">
                <button
                  onClick={(e) => handleAcceptFollowRequest(notification, e)}
                  className="px-3 py-1 bg-blue-500 text-white text-xs rounded-md hover:bg-blue-600 transition-colors flex items-center space-x-1"
                >
                  <CheckIcon className="w-3 h-3" />
                  <span>Chấp nhận</span>
                </button>
                <button
                  onClick={(e) => handleRejectFollowRequest(notification, e)}
                  className="px-3 py-1 bg-gray-200 text-gray-700 text-xs rounded-md hover:bg-gray-300 transition-colors flex items-center space-x-1"
                >
                  <XMarkIconSmall className="w-3 h-3" />
                  <span>Từ chối</span>
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <>
      {/* Overlay - covers full screen on mobile, only main content on desktop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-30 z-40 md:left-16 lg:left-72"
        onClick={() => navigate(-1)}
      />

      {/* Notifications Panel */}
      <div className="fixed left-0 md:left-16 lg:left-72 top-0 h-full w-full md:w-80 bg-white border-r border-gray-200 shadow-lg transform transition-transform duration-300 ease-out animate-slide-in-left z-50">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 px-4 py-4">
          <div className="flex items-center justify-between">
            <h1 className="text-xl font-semibold text-gray-900">
              Notifications
            </h1>
            <button
              onClick={() => navigate(-1)}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <XMarkIcon className="w-5 h-5 text-gray-600" />
            </button>
          </div>

          {/* Filter Tabs */}
          {/* <div className="flex mt-4 border-b border-gray-200">
              <button
                onClick={() => setFilter("all")}
                className={`flex-1 py-3 text-sm font-medium text-center border-b-2 transition-colors ${
                  filter === "all"
                    ? "border-black text-black"
                    : "border-transparent text-gray-500 hover:text-gray-700"
                }`}
              >
                All
              </button>
              <button
                onClick={() => setFilter("unread")}
                className={`flex-1 py-3 text-sm font-medium text-center border-b-2 transition-colors ${
                  filter === "unread"
                    ? "border-black text-black"
                    : "border-transparent text-gray-500 hover:text-gray-700"
                }`}
              >
                Unread
                {unreadCount > 0 && (
                  <span className="ml-1 bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5">
                    {unreadCount}
                  </span>
                )}
              </button>
            </div> */}
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          {filteredNotifications.length === 0 ? (
            <div className="p-8 text-center">
              <HeartIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Activity On Your Posts
              </h3>
              <p className="text-gray-500">
                When someone likes or comments on one of your posts, you'll see
                it here.
              </p>
            </div>
          ) : (
            <div>
              {/* This Month */}
              {groupedNotifications.today.length > 0 && (
                <div className="mb-6">
                  <div>
                    <h2 className="text-base font-semibold text-black px-4 py-3 bg-white sticky top-16 z-5">
                      Today
                    </h2>
                  </div>
                  <div className="divide-y divide-gray-100">
                    {groupedNotifications.today.map((notification) => (
                      <NotificationItem
                        key={notification.id}
                        notification={notification}
                      />
                    ))}
                  </div>
                </div>
              )}

              {/* This Week */}
              {groupedNotifications.thisWeek.length > 0 && (
                <div className="mb-6">
                  <div>
                    {" "}
                    <h2 className="text-base font-semibold text-black px-4 py-3 bg-white sticky top-16 z-5">
                      This Week
                    </h2>
                  </div>
                  <div className="divide-y divide-gray-100">
                    {groupedNotifications.thisWeek.map((notification) => (
                      <NotificationItem
                        key={notification.id}
                        notification={notification}
                      />
                    ))}
                  </div>
                </div>
              )}

              {/* This Month */}
              {groupedNotifications.thisMonth.length > 0 && (
                <div className="mb-6">
                  <div>
                    <h2 className="text-base font-semibold text-black px-4 py-3 bg-white sticky top-16 z-5">
                      This Month
                    </h2>
                  </div>
                  <div className="divide-y divide-gray-100">
                    {groupedNotifications.thisMonth.map((notification) => (
                      <NotificationItem
                        key={notification.id}
                        notification={notification}
                      />
                    ))}
                  </div>
                </div>
              )}

              {/* Earlier */}
              {groupedNotifications.earlier.length > 0 && (
                <div className="mb-6">
                  <h2 className="text-base font-semibold text-black px-4 py-3 bg-white sticky top-16 z-5">
                    Earlier
                  </h2>
                  <div className="divide-y divide-gray-100">
                    {groupedNotifications.earlier.map((notification) => (
                      <NotificationItem
                        key={notification.id}
                        notification={notification}
                      />
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default NotificationsPage;
