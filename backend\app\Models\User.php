<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use App\Models\Follow;
use App\Notifications\ResetPasswordNotification;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'username',
        'email',
        'phone',
        'password',
        'bio',
        'website',
        'gender',
        'avatar',
        'cover_image',
        'is_private',
        'last_active_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'last_active_at' => 'datetime',
        'is_private' => 'boolean',
    ];

    protected $appends = [
        'followers_count',
        'following_count',
        'posts_count',
        'is_following',
        'is_followed_by',
        'follow_status',
        'full_name',
    ];

    // Relationships
    public function posts()
    {
        return $this->hasMany(Post::class);
    }

    public function followers()
    {
        return $this->belongsToMany(User::class, 'follows', 'following_id', 'follower_id')
            ->withPivot('status')
            ->withTimestamps();
    }

    public function following()
    {
        return $this->belongsToMany(User::class, 'follows', 'follower_id', 'following_id')
            ->withPivot('status')
            ->withTimestamps();
    }

    public function likes()
    {
        return $this->hasMany(Like::class);
    }

    public function comments()
    {
        return $this->hasMany(Comment::class);
    }

    public function conversations()
    {
        return $this->belongsToMany(Conversation::class, 'conversation_participants')
            ->withPivot(['joined_at', 'last_read_at', 'is_admin'])
            ->withTimestamps();
    }

    public function messages()
    {
        return $this->hasMany(Message::class);
    }

    public function notifications()
    {
        return $this->hasMany(Notification::class);
    }

    // Accessors
    public function getFullNameAttribute()
    {
        return $this->name;
    }

    public function getAvatarAttribute($value)
    {
        if (!$value) {
            return null;
        }
        
        // If it's already a full URL, return as is
        if (str_starts_with($value, 'http')) {
            return $value;
        }
        
        // Return full URL with storage path
        return url('storage/' . $value);
    }

    public function getCoverImageAttribute($value)
    {
        if (!$value) {
            return null;
        }
        
        // If it's already a full URL, return as is
        if (str_starts_with($value, 'http')) {
            return $value;
        }
        
        // Return full URL with storage path
        return url('storage/' . $value);
    }

    public function getIsFollowingAttribute()
    {
        if (!auth()->check()) {
            return false;
        }
        /** @var \App\Models\User $currentUser */
        $currentUser = auth()->user();
        return $currentUser->isFollowing($this);
    }

    public function getIsFollowedByAttribute()
    {
        if (!auth()->check()) {
            return false;
        }
        /** @var \App\Models\User $currentUser */
        $currentUser = auth()->user();
        return $currentUser->isFollowedBy($this);
    }

    public function getFollowStatusAttribute()
    {
        if (!auth()->check()) {
            return null;
        }
        /** @var \App\Models\User $currentUser */
        $currentUser = auth()->user();
        return $currentUser->getFollowStatus($this);
    }

    // Helper methods
    public function isFollowing(User $user)
    {
        return $this->following()->wherePivot('status', 'accepted')->whereKey($user->id)->exists();
    }

    public function isFollowedBy(User $user)
    {
        return $this->followers()->wherePivot('status', 'accepted')->whereKey($user->id)->exists();
    }

    // Check if following with any status (including pending)
    public function hasFollowRequest(User $user)
    {
        return Follow::where('follower_id', $this->id)
            ->where('following_id', $user->id)
            ->exists();
    }

    // Get follow status with user
    public function getFollowStatus(User $user)
    {
        $follow = Follow::where('follower_id', $this->id)
            ->where('following_id', $user->id)
            ->first();

        return $follow ? $follow->status : null;
    }

    public function getFollowersCountAttribute()
    {
        return $this->followers()->wherePivot('status', 'accepted')->count();
    }

    public function getFollowingCountAttribute()
    {
        return $this->following()->wherePivot('status', 'accepted')->count();
    }

    public function getPostsCountAttribute()
    {
        return $this->posts()->count();
    }

    /**
     * Send the password reset notification.
     *
     * @param  string  $token
     * @return void
     */
    public function sendPasswordResetNotification($token)
    {
        $this->notify(new ResetPasswordNotification($token));
    }
}