<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Comment extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'post_id',
        'parent_id',
        'content',
        'likes_count',
        'replies_count',
    ];

    protected $casts = [
        'likes_count' => 'integer',
        'replies_count' => 'integer',
    ];

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function post()
    {
        return $this->belongsTo(Post::class);
    }

    public function parent()
    {
        return $this->belongsTo(Comment::class, 'parent_id');
    }

    public function replies()
    {
        return $this->hasMany(Comment::class, 'parent_id')->orderBy('created_at', 'asc');
    }

    public function likes()
    {
        return $this->morphMany(Like::class, 'likeable');
    }

    // Helper methods
    public function isLikedBy(User $user)
    {
        return $this->likes()->where('user_id', $user->id)->exists();
    }

    public function toggleLike(User $user)
    {
        $like = $this->likes()->where('user_id', $user->id)->first();

        if ($like) {
            $like->delete();
            $this->decrement('likes_count');
            return false;
        } else {
            $this->likes()->create(['user_id' => $user->id]);
            $this->increment('likes_count');
            return true;
        }
    }

    public function isReply()
    {
        return !is_null($this->parent_id);
    }

    protected static function boot()
    {
        parent::boot();

        static::created(function ($comment) {
            // Increment post comments count
            $comment->post->increment('comments_count');

            // If this is a reply, increment parent comment replies count
            if ($comment->parent_id) {
                $comment->parent->increment('replies_count');
            }
        });

        static::deleted(function ($comment) {
            // Decrement post comments count
            $comment->post->decrement('comments_count');

            // If this is a reply, decrement parent comment replies count
            if ($comment->parent_id) {
                $comment->parent->decrement('replies_count');
            }
        });
    }
}
