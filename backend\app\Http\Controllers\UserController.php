<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class UserController extends Controller
{
    public function index(Request $request)
    {
        $query = User::query();

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('username', 'like', "%{$search}%")
                    ->orWhere('name', 'like', "%{$search}%");
            });
        }

        $users = $query->paginate(20);

        return response()->json([
            'success' => true,
            'data' => $users
        ]);
    }

    public function show(Request $request, $username)
    {
        $user = User::where('username', $username)->firstOrFail();
        $currentUser = $request->user();

        $user->load(['posts' => function ($query) {
            $query->with('media')->latest()->take(12);
        }]);

        // Add follow status if user is authenticated
        if ($currentUser && $currentUser->id !== $user->id) {
            $user->follow_status = $currentUser->getFollowStatus($user);
        } else {
            $user->follow_status = null;
        }

        return response()->json([
            'success' => true,
            'data' => $user
        ]);
    }

    public function updateProfile(Request $request)
    {
        $user = $request->user();

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|max:255',
            'username' => [
                'sometimes',
                'string',
                'max:255',
                'regex:/^[a-zA-Z0-9._]+$/',
                Rule::unique('users')->ignore($user->id)
            ],
            'email' => [
                'sometimes',
                'email',
                'max:255',
                Rule::unique('users')->ignore($user->id)
            ],
            'phone' => [
                'nullable',
                'string',
                'max:20',
                Rule::unique('users')->ignore($user->id)
            ],
            'bio' => 'nullable|string|max:500',
            'website' => 'nullable|url|max:255',
            'gender' => 'nullable|string|in:male,female,other,prefer_not_to_say',
            'is_private' => 'sometimes|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user->update($request->only([
            'name',
            'username',
            'email',
            'phone',
            'bio',
            'website',
            'gender',
            'is_private'
        ]));

        return response()->json([
            'success' => true,
            'message' => 'Profile updated successfully',
            'data' => $user
        ]);
    }

    public function updateAvatar(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'avatar' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();

        // Delete old avatar if exists
        if ($user->avatar && Storage::exists($user->avatar)) {
            Storage::delete($user->avatar);
        }

        // Store new avatar
        $avatarPath = $request->file('avatar')->store('avatars', 'public');

        $user->update(['avatar' => $avatarPath]);

        // Refresh user to get updated avatar with accessor
        $user->refresh();

        return response()->json([
            'success' => true,
            'message' => 'Avatar updated successfully',
            'data' => $user
        ]);
    }

    public function updateCoverImage(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'cover_image' => 'required|image|mimes:jpeg,png,jpg,gif|max:5120', // 5MB max
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();

        // Delete old cover image if exists
        if ($user->cover_image && Storage::exists($user->cover_image)) {
            Storage::delete($user->cover_image);
        }

        // Store new cover image
        $coverPath = $request->file('cover_image')->store('covers', 'public');

        $user->update(['cover_image' => $coverPath]);

        // Refresh user to get updated cover image with accessor
        $user->refresh();

        return response()->json([
            'success' => true,
            'message' => 'Cover image updated successfully',
            'data' => $user
        ]);
    }

    public function removeAvatar(Request $request)
    {
        $user = $request->user();

        if ($user->avatar && Storage::exists($user->avatar)) {
            Storage::delete($user->avatar);
        }

        $user->update(['avatar' => null]);

        return response()->json([
            'success' => true,
            'message' => 'Avatar removed successfully'
        ]);
    }

    public function removeCoverImage(Request $request)
    {
        $user = $request->user();

        if ($user->cover_image && Storage::exists($user->cover_image)) {
            Storage::delete($user->cover_image);
        }

        $user->update(['cover_image' => null]);

        return response()->json([
            'success' => true,
            'message' => 'Cover image removed successfully'
        ]);
    }

    public function followers($username)
    {
        $user = User::where('username', $username)->firstOrFail();

        $followers = $user->followers()->paginate(20);

        return response()->json([
            'success' => true,
            'data' => $followers
        ]);
    }

    public function following($username)
    {
        $user = User::where('username', $username)->firstOrFail();

        $following = $user->following()->paginate(20);

        return response()->json([
            'success' => true,
            'data' => $following
        ]);
    }

    public function posts($username)
    {
        $user = User::where('username', $username)->firstOrFail();

        $posts = $user->posts()
            ->with(['media', 'user'])
            ->withCount(['likes', 'comments'])
            ->latest()
            ->paginate(12);

        // Transform media to include full URLs
        $posts->getCollection()->transform(function ($post) {
            $post->media->transform(function ($media) {
                $media->file_url = $media->file_path ? url(Storage::url($media->file_path)) : null;
                $media->thumbnail_url = $media->thumbnail_path ? url(Storage::url($media->thumbnail_path)) : null;
                return $media;
            });
            return $post;
        });

        return response()->json([
            'success' => true,
            'data' => $posts
        ]);
    }

    public function search(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'q' => 'required|string|min:1',
            'page' => 'sometimes|integer|min:1'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $query = $request->q;
        $page = $request->get('page', 1);

        $users = User::where(function ($q) use ($query) {
                $q->where('username', 'like', "%{$query}%")
                    ->orWhere('name', 'like', "%{$query}%");
            })
            ->select(['id', 'username', 'name', 'avatar'])
            ->paginate(20, ['*'], 'page', $page);

        // Transform users to include additional data
        $users->getCollection()->transform(function ($user) {
            $user->followers_count = $user->followers()->count();
            $user->following_count = $user->following()->count();
            $user->posts_count = $user->posts()->count();
            $user->is_following = false;
            $user->is_followed_by = false;
            $user->follow_status = null;
            $user->full_name = $user->name;
            return $user;
        });

        return response()->json([
            'success' => true,
            'data' => $users->items(),
            'current_page' => $users->currentPage(),
            'last_page' => $users->lastPage(),
            'per_page' => $users->perPage(),
            'total' => $users->total()
        ]);
    }

    public function getSuggestedUsers(Request $request)
    {
        $currentUser = $request->user();
        
        if (!$currentUser) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 401);
        }
        
        $limit = $request->get('limit', 10);

        // Get users that the current user is not following
        $suggestedUsers = User::where('id', '!=', $currentUser->id)
            ->whereNotIn('id', function ($query) use ($currentUser) {
                $query->select('followed_id')
                    ->from('follows')
                    ->where('follower_id', $currentUser->id);
            })
            ->withCount(['followers', 'following', 'posts'])
            ->inRandomOrder()
            ->limit($limit)
            ->get();

        // Add suggestion reasons and mutual followers info
        $suggestedUsers->each(function ($user) use ($currentUser) {
            // Get mutual followers count
            $mutualFollowersCount = $currentUser->following()
                ->whereIn('followed_id', function ($query) use ($user) {
                    $query->select('follower_id')
                        ->from('follows')
                        ->where('followed_id', $user->id);
                })
                ->count();

            $user->mutual_followers_count = $mutualFollowersCount;
            
            // Get some mutual followers for display
            if ($mutualFollowersCount > 0) {
                $mutualFollowers = $currentUser->following()
                    ->whereIn('followed_id', function ($query) use ($user) {
                        $query->select('follower_id')
                            ->from('follows')
                            ->where('followed_id', $user->id);
                    })
                    ->limit(3)
                    ->get(['id', 'username', 'name as full_name']);
                
                $user->mutual_followers = $mutualFollowers;
                $user->suggestion_reason = 'Có bạn chung';
            } else {
                $user->mutual_followers = [];
                // Random suggestion reasons
                $reasons = ['Được đề xuất cho bạn', 'Phổ biến', 'Từ danh bạ'];
                $user->suggestion_reason = $reasons[array_rand($reasons)];
            }

            // Add avatar URL if exists
            if ($user->avatar) {
                $user->avatar = url(Storage::url($user->avatar));
            }
        });

        return response()->json([
            'success' => true,
            'data' => $suggestedUsers
        ]);
    }
}