import React from "react";
import { usePosts } from "../hooks/usePosts";
import PostCard from "../components/posts/PostCard";
import SuggestedUsers from "../components/users/SuggestedUsers";
import LoadingSpinner from "../components/ui/LoadingSpinner";
import InfiniteScroll from "react-infinite-scroll-component";

const HomePage: React.FC = () => {
  const { posts, isLoading, hasMore, loadMore, toggleLike, toggleSave } =
    usePosts({ type: "feed" });

  if (isLoading && posts.length === 0) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-instagram-light-gray">
      <div className="max-w-6xl mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Feed */}
          <div className="lg:col-span-2">
            {/* Stories */}
            {/* <div className="mb-8">
              <Stories />
            </div> */}

            {/* Posts */}
            <div className="space-y-6">
              {posts.length === 0 && !isLoading ? (
                <div className="text-center py-12">
                  <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg
                      className="w-12 h-12 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                      />
                    </svg>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    Chào mừng đến với Instagram!
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Khi bạn theo dõi mọi người, bạn sẽ thấy ảnh và video họ chia
                    sẻ ở đây.
                  </p>
                  <button className="text-instagram-blue font-semibold hover:underline">
                    Tìm người để theo dõi
                  </button>
                </div>
              ) : (
                <InfiniteScroll
                  dataLength={posts.length}
                  next={loadMore}
                  hasMore={hasMore}
                  loader={
                    <div className="flex justify-center py-4">
                      <LoadingSpinner size="md" />
                    </div>
                  }
                  endMessage={
                    <div className="text-center py-8 text-gray-500">
                      <p>Bạn đã xem hết bài viết!</p>
                    </div>
                  }
                >
                  {posts.map((post) => (
                    <PostCard
                      key={post.id}
                      post={post}
                      onLike={() => toggleLike(post.id)}
                      onSave={() => toggleSave(post.id)}
                    />
                  ))}
                </InfiniteScroll>
              )}
            </div>
          </div>

          {/* Sidebar */}
          <div className="hidden lg:block">
            <div className="sticky top-8 space-y-6">
              {/* Suggested Users */}
              <SuggestedUsers />

              {/* Footer */}
              <div className="text-xs text-gray-400 space-y-2">
                <div className="flex flex-wrap gap-2">
                  <a href="#" className="hover:underline">
                    Giới thiệu
                  </a>
                  <a href="#" className="hover:underline">
                    Trợ giúp
                  </a>
                  <a href="#" className="hover:underline">
                    Báo chí
                  </a>
                  <a href="#" className="hover:underline">
                    API
                  </a>
                  <a href="#" className="hover:underline">
                    Việc làm
                  </a>
                  <a href="#" className="hover:underline">
                    Quyền riêng tư
                  </a>
                  <a href="#" className="hover:underline">
                    Điều khoản
                  </a>
                </div>
                <div className="flex flex-wrap gap-2">
                  <a href="#" className="hover:underline">
                    Vị trí
                  </a>
                  <a href="#" className="hover:underline">
                    Ngôn ngữ
                  </a>
                  <a href="#" className="hover:underline">
                    Meta Verified
                  </a>
                </div>
                <p className="mt-4">© 2024 Instagram Clone</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HomePage;
