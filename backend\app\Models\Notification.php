<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Notification extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'type',
        'data',
        'is_read',
    ];

    protected $casts = [
        'data' => 'array',
        'is_read' => 'boolean',
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // Scopes
    public function scopeUnread($query)
    {
        return $query->where('is_read', false);
    }

    public function scopeRead($query)
    {
        return $query->where('is_read', true);
    }

    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    // Helper methods
    public function markAsRead()
    {
        $this->update(['is_read' => true]);
    }

    public function getActorAttribute()
    {
        if (isset($this->data['actor_id'])) {
            return User::find($this->data['actor_id']);
        }
        return null;
    }

    public function getPostAttribute()
    {
        if (isset($this->data['post_id'])) {
            return Post::find($this->data['post_id']);
        }
        return null;
    }

    public function getCommentAttribute()
    {
        if (isset($this->data['comment_id'])) {
            return Comment::find($this->data['comment_id']);
        }
        return null;
    }
}