import React, { useState, useEffect, useRef, useCallback } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";

import LoadingSpinner from "../components/ui/LoadingSpinner";
import PostCard from "../components/posts/PostCard";
import ProfilePostCard from "../components/posts/ProfilePostCard";
import {
  MagnifyingGlassIcon,
  XMarkIcon,
  ClockIcon,
} from "@heroicons/react/24/outline";
import { toast } from "react-hot-toast";
import { usersService } from "../services/users";
import { postsService } from "../services/posts";
import type { User, Post, PaginatedResponse } from "../types";

interface SearchResult {
  users: User[];
  posts: Post[];
}

const SearchPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const [query, setQuery] = useState(searchParams.get("q") || "");
  const [activeTab, setActiveTab] = useState<
    "top" | "accounts" | "posts"
  >("top");
  const [searchResults, setSearchResults] = useState<SearchResult>({
    users: [],
    posts: [],
  });
  const [loading, setLoading] = useState(false);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    // Load recent searches from localStorage
    const saved = localStorage.getItem("instagram_recent_searches");
    if (saved) {
      setRecentSearches(JSON.parse(saved));
    }
  }, []);

  const addToRecentSearches = useCallback((searchQuery: string) => {
    const updated = [
      searchQuery,
      ...recentSearches.filter((s) => s !== searchQuery),
    ].slice(0, 10);
    setRecentSearches(updated);
    localStorage.setItem("instagram_recent_searches", JSON.stringify(updated));
  }, [recentSearches]);

  const performSearch = useCallback(async (searchQuery: string) => {
    if (!searchQuery.trim()) {
      setSearchResults({ users: [], posts: [] });
      return;
    }

    try {
      setLoading(true);

      // Search users and posts in parallel
      const [usersResponse, postsResponse] = await Promise.all([
        usersService.searchUsers(searchQuery, 1),
        postsService.searchPosts(searchQuery, 1)
      ]);

      setSearchResults({
        users: usersResponse.data,
        posts: postsResponse.data,
      });

      // Add to recent searches
      addToRecentSearches(searchQuery);
    } catch (error) {
      console.error("Error searching:", error);
      toast.error("Không thể tìm kiếm");
    } finally {
      setLoading(false);
    }
  }, [addToRecentSearches]);

  useEffect(() => {
    const queryParam = searchParams.get("q");
    if (queryParam && queryParam !== query) {
      setQuery(queryParam);
      performSearch(queryParam);
    }
  }, [searchParams, query, performSearch]);

  const removeFromRecentSearches = (searchQuery: string) => {
    const updated = recentSearches.filter((s) => s !== searchQuery);
    setRecentSearches(updated);
    localStorage.setItem("instagram_recent_searches", JSON.stringify(updated));
  };

  const clearRecentSearches = () => {
    setRecentSearches([]);
    localStorage.removeItem("instagram_recent_searches");
  };

  const handleSearch = (searchQuery: string) => {
    setQuery(searchQuery);
    setSearchParams({ q: searchQuery });
    setShowSuggestions(false);
    performSearch(searchQuery);
  };

  const handleInputChange = (value: string) => {
    setQuery(value);
    if (value.trim()) {
      setShowSuggestions(false);
      performSearch(value);
    } else {
        setSearchResults({ users: [], posts: [] });
        setShowSuggestions(true);
      }
  };

  const formatCount = (count: number): string => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  const tabs = [
    { key: "top", label: "Hàng đầu" },
    { key: "accounts", label: "Tài khoản" },
    { key: "posts", label: "Bài viết" },
  ] as const;

  const renderUserResult = (user: User) => (
    <div
      key={user.id}
      onClick={() => navigate(`/${user.username}`)}
      className="flex items-center justify-between p-4 hover:bg-gray-50 cursor-pointer"
    >
      <div className="flex items-center space-x-3">
        <img
          src={user.avatar || "https://api-private.atlassian.com/users/8b3597e8a7d1d8f2ed7f58bcab1946b8/avatar"}
          alt={user.username}
          className="w-12 h-12 rounded-full object-cover"
        />
        <div>
          <div className="flex items-center space-x-1">
            <span className="font-semibold">{user.username}</span>
            {user.is_verified && <span className="text-blue-500">✓</span>}
          </div>
          <div className="text-sm text-gray-500">
            {user.full_name} • {formatCount(user.followers_count || 0)} người theo
            dõi
          </div>
        </div>
      </div>

      {user.id !== user?.id && (
        <button
          onClick={(e) => {
            e.stopPropagation();
            toast("Tính năng đang được phát triển");
          }}
          className={`px-4 py-1.5 rounded-md text-sm font-medium ${
            user.is_following
              ? "bg-gray-200 text-gray-900 hover:bg-gray-300"
              : "bg-blue-500 text-white hover:bg-blue-600"
          }`}
        >
          {user.is_following ? "Đang theo dõi" : "Theo dõi"}
        </button>
      )}
    </div>
  );



  return (
    <div className="min-h-screen bg-gray-50">
      {/* Search Header */}
      <div className="bg-white border-b border-gray-200 px-4 py-3 sticky top-0 z-10">
        <div className="max-w-2xl mx-auto">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              ref={searchInputRef}
              type="text"
              value={query}
              onChange={(e) => handleInputChange(e.target.value)}
              onFocus={() => setShowSuggestions(!query.trim())}
              placeholder="Tìm kiếm"
              className="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-lg leading-5 bg-gray-100 placeholder-gray-500 focus:outline-none focus:bg-white focus:border-gray-500 focus:ring-1 focus:ring-gray-500"
            />
            {query && (
              <button
                onClick={() => {
                  setQuery("");
                  setSearchParams({});
                  setSearchResults({
                    users: [],
                    posts: [],
                  });
                  setShowSuggestions(true);
                }}
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
              >
                <XMarkIcon className="h-5 w-5 text-gray-400 hover:text-gray-600" />
              </button>
            )}
          </div>
        </div>
      </div>

      <div className="max-w-2xl mx-auto">
        {/* Recent Searches / Suggestions */}
        {showSuggestions && !query.trim() && (
          <div className="bg-white">
            {recentSearches.length > 0 && (
              <div className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-semibold text-gray-900">
                    Tìm kiếm gần đây
                  </h3>
                  <button
                    onClick={clearRecentSearches}
                    className="text-blue-500 hover:text-blue-600 text-sm font-medium"
                  >
                    Xóa tất cả
                  </button>
                </div>
                <div className="space-y-2">
                  {recentSearches.map((search, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-2 hover:bg-gray-50 rounded-lg cursor-pointer"
                      onClick={() => handleSearch(search)}
                    >
                      <div className="flex items-center space-x-3">
                        <ClockIcon className="w-5 h-5 text-gray-400" />
                        <span className="text-gray-900">{search}</span>
                      </div>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          removeFromRecentSearches(search);
                        }}
                        className="p-1 hover:bg-gray-200 rounded-full"
                      >
                        <XMarkIcon className="w-4 h-4 text-gray-400" />
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Search Results */}
        {query.trim() && (
          <>
            {/* Tabs */}
            <div className="bg-white border-b border-gray-200">
              <div className="flex space-x-8 px-4">
                {tabs.map((tab) => (
                  <button
                    key={tab.key}
                    onClick={() => setActiveTab(tab.key)}
                    className={`py-3 border-b-2 font-medium text-sm ${
                      activeTab === tab.key
                        ? "border-black text-black"
                        : "border-transparent text-gray-500 hover:text-gray-700"
                    }`}
                  >
                    {tab.label}
                  </button>
                ))}
              </div>
            </div>

            {/* Results Content */}
            <div className="bg-white">
              {loading ? (
                <div className="flex items-center justify-center py-12">
                  <LoadingSpinner size="lg" />
                </div>
              ) : (
                <div>
                  {activeTab === "top" && (
                    <div className="divide-y divide-gray-100">
                      {/* Top Users */}
                      {searchResults.users.slice(0, 3).map(renderUserResult)}
                    </div>
                  )}

                  {activeTab === "accounts" && (
                    <div className="divide-y divide-gray-100">
                      {searchResults.users.length > 0 ? (
                        searchResults.users.map(renderUserResult)
                      ) : (
                        <div className="p-8 text-center text-gray-500">
                          Không tìm thấy tài khoản nào
                        </div>
                      )}
                    </div>
                  )}

                  {activeTab === "posts" && (
                    <div className="p-4">
                      {searchResults.posts.length > 0 ? (
                        <div className="grid grid-cols-3 gap-1 md:gap-4">
                          {searchResults.posts.map((post) => (
                            <ProfilePostCard 
                              key={post.id} 
                              post={post} 
                              onLike={() => {}}
                              onSave={() => {}}
                            />
                          ))}
                        </div>
                      ) : (
                        <div className="p-8 text-center text-gray-500">
                          Không tìm thấy bài viết nào
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Posts Preview in Top Tab */}
            {activeTab === "top" && searchResults.posts.length > 0 && (
              <div className="mt-1">
                <div className="bg-white p-4 border-b border-gray-200">
                  <h3 className="font-semibold text-gray-900">Bài viết</h3>
                </div>
                <div className="p-4">
                  <div className="grid grid-cols-3 gap-1 md:gap-4">
                    {searchResults.posts.slice(0, 6).map((post) => (
                      <ProfilePostCard 
                        key={post.id} 
                        post={post} 
                        onLike={() => {}}
                        onSave={() => {}}
                      />
                    ))}
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default SearchPage;
