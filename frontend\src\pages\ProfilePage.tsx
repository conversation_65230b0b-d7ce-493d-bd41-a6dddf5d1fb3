import React, { useState, useEffect, useMemo } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";
import { usePosts } from "../hooks/usePosts";
import PostCard from "../components/posts/PostCard";
import ProfilePostCard from "../components/posts/ProfilePostCard";
import LoadingSpinner from "../components/ui/LoadingSpinner";
import { usersService } from "../services/users";
import {
  Cog6ToothIcon,
  UserPlusIcon,
  UserMinusIcon,
  ChatBubbleOvalLeftIcon,
  EllipsisHorizontalIcon,
} from "@heroicons/react/24/outline";
import { toast } from "react-hot-toast";

import type { User } from "../types";

const ProfilePage: React.FC = () => {
  const { username } = useParams<{ username: string }>();
  const navigate = useNavigate();
  const { user: currentUser } = useAuth();
  const [profile, setProfile] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [followLoading, setFollowLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<"posts" | "saved">("posts");

  const isOwnProfile = currentUser.user
    ? currentUser?.user?.username === username
    : currentUser.username === username;
  // Debug log to check the comparison
  console.log("Debug isOwnProfile:", {
    currentUser: currentUser,
    currentUserUsername: currentUser?.user?.username,
    urlUsername: username,
    isOwnProfile,
  });

  // Dynamic usePosts based on activeTab and profile
  const postsOptions = useMemo(() => {
    if (activeTab === "saved" && isOwnProfile) {
      return { type: "saved" as const };
    } else if (username) {
      return { type: "userByUsername" as const, username };
    }
    return { type: "feed" as const };
  }, [activeTab, isOwnProfile, username]);

  const {
    posts,
    isLoading: postsLoading,
    error: postsError,
    hasMore,
    loadMore,
  } = usePosts(postsOptions);
  useEffect(() => {
    if (username) {
      fetchProfile();
    }
  }, [username]);

  // usePosts will automatically fetch when postsOptions change
  // No need to manually call fetchPosts as usePosts handles it automatically

  const fetchProfile = async () => {
    try {
      setLoading(true);
      if (!username) return;

      const userProfile = await usersService.getUserByUsername(username);
      setProfile(userProfile);
    } catch (error) {
      console.error("Error fetching profile:", error);
      toast.error("Không thể tải thông tin người dùng");
    } finally {
      setLoading(false);
    }
  };

  const handleFollow = async () => {
    if (!profile) return;

    const oldStatus = profile.follow_status;

    setFollowLoading(true);

    try {
      // Call API based on current status
      if (oldStatus === "accepted" || oldStatus === "pending") {
        // If previously following or request sent, unfollow/cancel
        const result = await usersService.toggleFollowByUsername(
          profile.username
        );
        toast.success(
          oldStatus === "accepted"
            ? "Đã bỏ theo dõi"
            : "Đã hủy yêu cầu theo dõi"
        );
        // Update profile with new status
        setProfile((prev) =>
          prev
            ? {
                ...prev,
                follow_status: result.status || null,
                followers_count: result.followers_count,
              }
            : null
        );
      } else {
        // If not following, send follow request (always creates a request now)
        const result = await usersService.toggleFollowByUsername(
          profile.username
        );
        toast.success("Đã gửi yêu cầu theo dõi");
        // Update profile with new status
        setProfile((prev) =>
          prev
            ? {
                ...prev,
                follow_status: result.status || "pending",
                followers_count: result.followers_count,
              }
            : null
        );
      }
    } catch (error) {
      console.error("Error following/unfollowing user:", error);
      toast.error("Có lỗi xảy ra");
    } finally {
      setFollowLoading(false);
    }
  };

  const renderFollowButton = () => {
    if (followLoading) {
      return (
        <button
          disabled
          className="px-4 py-1.5 rounded-lg text-sm font-medium transition-colors flex items-center justify-center w-28 h-8 bg-gray-200"
        >
          <LoadingSpinner size="sm" />
        </button>
      );
    }
    console.log("profile", profile);
    switch (profile?.follow_status) {
      case "accepted":
        return (
          <button
            onClick={handleFollow}
            className="px-4 py-1.5 rounded-lg text-sm font-medium transition-colors flex items-center space-x-1 border border-gray-300 text-gray-900 hover:bg-gray-50"
          >
            <UserMinusIcon className="w-4 h-4" />
            <span>Đang theo dõi</span>
          </button>
        );
      case "pending":
        return (
          <button
            onClick={handleFollow}
            className="px-4 py-1.5 rounded-lg text-sm font-medium transition-colors flex items-center space-x-1 border border-orange-300 text-orange-700 hover:bg-orange-50"
          >
            <UserMinusIcon className="w-4 h-4" />
            <span>Chờ chấp nhận</span>
          </button>
        );
      default:
        return (
          <button
            onClick={handleFollow}
            className="px-4 py-1.5 rounded-lg text-sm font-medium transition-colors flex items-center space-x-1 bg-blue-500 text-white hover:bg-blue-600"
          >
            <UserPlusIcon className="w-4 h-4" />
            <span>Theo dõi</span>
          </button>
        );
    }
  };

  const formatCount = (count: number | undefined): string => {
    if (!count && count !== 0) {
      return "0";
    }
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Không tìm thấy người dùng
        </h2>
        <p className="text-gray-600 mb-4">
          Người dùng này có thể đã bị xóa hoặc không tồn tại.
        </p>
        <button
          onClick={() => navigate("/")}
          className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
        >
          Về trang chủ
        </button>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      {/* Profile Header */}
      <div className="flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-8 mb-8">
        {/* Avatar */}
        <div className="flex-shrink-0">
          <img
            src={
              profile.avatar ||
              "https://api-private.atlassian.com/users/8b3597e8a7d1d8f2ed7f58bcab1946b8/avatar"
            }
            alt={profile.username}
            className="w-32 h-32 md:w-40 md:h-40 rounded-full object-cover border-4 border-gray-200"
          />
        </div>

        {/* Profile Info */}
        <div className="flex-1 w-full">
          {/* Username and Actions */}
          <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4 mb-4">
            <h1 className="text-2xl font-light">{profile.username}</h1>

            <div className="flex space-x-2">
              {/* Debug log for button rendering */}
              {(() => {
                console.log(
                  "Rendering buttons - isOwnProfile:",
                  isOwnProfile,
                  "currentUser:",
                  currentUser?.username
                );
                return null;
              })()}
              {isOwnProfile ? (
                <>
                  <button
                    onClick={() => navigate("/accounts/edit")}
                    className="px-4 py-1.5 border border-gray-300 rounded-lg text-sm font-medium hover:bg-gray-50 transition-colors"
                  >
                    Chỉnh sửa trang cá nhân
                  </button>
                </>
              ) : (
                <>
                  {renderFollowButton()}

                  <button
                    onClick={() =>
                      navigate(`/messages?user=${profile.username}`)
                    }
                    className="px-4 py-1.5 border border-gray-300 rounded-lg text-sm font-medium hover:bg-gray-50 transition-colors flex items-center space-x-1"
                  >
                    <ChatBubbleOvalLeftIcon className="w-4 h-4" />
                    <span>Nhắn tin</span>
                  </button>

                  <button className="p-1.5 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                    <EllipsisHorizontalIcon className="w-5 h-5" />
                  </button>
                </>
              )}
            </div>
          </div>

          {/* Stats */}
          <div className="flex space-x-8 mb-4">
            <div className="text-center">
              <div className="font-semibold">
                {formatCount(profile.posts_count)}
              </div>
              <div className="text-gray-600 text-sm">bài viết</div>
            </div>
            <div className="text-center cursor-pointer hover:opacity-75">
              <div className="font-semibold">
                {formatCount(profile.followers_count)}
              </div>
              <div className="text-gray-600 text-sm">người theo dõi</div>
            </div>
            <div className="text-center cursor-pointer hover:opacity-75">
              <div className="font-semibold">
                {formatCount(profile.following_count)}
              </div>
              <div className="text-gray-600 text-sm">đang theo dõi</div>
            </div>
          </div>

          {/* Bio */}
          <div className="space-y-1">
            <div className="font-semibold">{profile.full_name}</div>
            {profile.bio && (
              <div className="text-sm whitespace-pre-line">{profile.bio}</div>
            )}
            {profile.website && (
              <a
                href={profile.website}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-900 text-sm hover:underline"
              >
                {profile.website}
              </a>
            )}

            {/* Contact Information */}
            <div className="mt-2 space-y-1">
              {profile.email && (
                <div className="flex items-center text-sm text-gray-600">
                  <span className="mr-2">📧</span>
                  <span>{profile.email}</span>
                </div>
              )}
              {profile.phone && (
                <div className="flex items-center text-sm text-gray-600">
                  <span className="mr-2">📱</span>
                  <span>{profile.phone}</span>
                </div>
              )}
              {profile.gender && (
                <div className="flex items-center text-sm text-gray-600">
                  <span className="mr-2">👤</span>
                  <span>
                    {profile.gender === "male" && "Nam"}
                    {profile.gender === "female" && "Nữ"}
                    {profile.gender === "other" && "Khác"}
                    {profile.gender === "prefer_not_to_say" &&
                      "Không muốn tiết lộ"}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-t border-gray-200">
        <div className="flex justify-center space-x-16">
          <button
            onClick={() => setActiveTab("posts")}
            className={`py-3 text-xs font-semibold uppercase tracking-wide border-t-2 transition-colors ${
              activeTab === "posts"
                ? "border-gray-900 text-gray-900"
                : "border-transparent text-gray-500 hover:text-gray-700"
            }`}
          >
            Bài viết
          </button>
        </div>
      </div>

      {/* Posts Grid */}
      <div className="mt-8">
        {profile.is_private &&
        !isOwnProfile &&
        profile.follow_status !== "following" ? (
          <div className="text-center py-16">
            <div className="text-2xl font-light mb-4">
              Tài khoản này ở chế độ riêng tư
            </div>
            <div className="text-gray-600">
              Theo dõi để xem ảnh và video của họ.
            </div>
          </div>
        ) : postsLoading && posts.length === 0 ? (
          <div className="flex justify-center py-8">
            <LoadingSpinner size="lg" />
          </div>
        ) : posts.length === 0 ? (
          <div className="text-center py-16">
            <div className="text-2xl font-light mb-4">
              {activeTab === "posts"
                ? "Chưa có bài viết nào"
                : "Chưa có bài viết đã lưu"}
            </div>
            <div className="text-gray-600">
              {activeTab === "posts"
                ? "Khi bạn chia sẻ ảnh, ảnh sẽ xuất hiện trên trang cá nhân của bạn."
                : "Lưu ảnh và video mà bạn muốn xem lại."}
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-3 gap-1 md:gap-4">
            {posts.map((post) => (
              <ProfilePostCard
                key={post.id}
                post={post}
                onLike={() => {}}
                onSave={() => {}}
              />
            ))}
          </div>
        )}

        {hasMore && posts.length > 0 && (
          <div className="flex justify-center mt-8">
            <button
              onClick={loadMore}
              disabled={postsLoading}
              className="px-6 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors disabled:opacity-50"
            >
              {postsLoading ? <LoadingSpinner size="sm" /> : "Tải thêm"}
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProfilePage;
