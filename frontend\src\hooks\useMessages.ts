import { useState, useEffect, useCallback, useRef } from 'react';
import type { Conversation, Message, User } from '../types';
import { messagesService } from '../services/messages';
import { socketService } from '../services/socket';
import { useAuth } from '../contexts/AuthContext';
import toast from 'react-hot-toast';

interface UseMessagesOptions {
  conversationId?: number;
  autoConnect?: boolean;
}

export const useMessages = (options: UseMessagesOptions = {}) => {
  const { conversationId, autoConnect = true } = options;
  const { user } = useAuth();

  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [currentConversation, setCurrentConversation] = useState<Conversation | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [typingUsers, setTypingUsers] = useState<{ [conversationId: number]: User[] }>({});
  const [onlineUsers, setOnlineUsers] = useState<Set<number>>(new Set());
  const [unreadCount, setUnreadCount] = useState(0);

  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Fetch conversations
  const fetchConversations = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await messagesService.getConversations();
      setConversations(response.data);
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to fetch conversations';
      toast.error(message);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Fetch messages for a conversation
  const fetchMessages = useCallback(async (convId: number, page: number = 1, reset: boolean = false) => {
    try {
      if (page === 1) {
        setIsLoading(true);
      } else {
        setIsLoadingMore(true);
      }

      console.log(`Fetching messages for conversation ${convId}, page ${page}, reset: ${reset}`);
      const response = await messagesService.getConversationMessages(convId, page);
      console.log(`Received ${response.data.length} messages:`, response.data);

      if (reset || page === 1) {
        // For first page, sort by created_at and id to ensure correct order
        const sortedMessages = response.data.sort((a, b) => {
          const dateA = new Date(a.created_at).getTime();
          const dateB = new Date(b.created_at).getTime();
          if (dateA === dateB) {
            return a.id - b.id; // If same timestamp, sort by ID
          }
          return dateA - dateB; // Oldest first, newest last
        });
        console.log('Setting messages (reset):', sortedMessages);
        setMessages(sortedMessages);
      } else {
        // For pagination, add older messages to the beginning
        const sortedNewMessages = response.data.sort((a, b) => {
          const dateA = new Date(a.created_at).getTime();
          const dateB = new Date(b.created_at).getTime();
          if (dateA === dateB) {
            return a.id - b.id;
          }
          return dateA - dateB;
        });
        setMessages(prev => [...sortedNewMessages, ...prev]);
      }

      setHasMore(page < response.last_page);
      setCurrentPage(page);
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to fetch messages';
      toast.error(message);
      console.error('Error fetching messages:', error);
    } finally {
      setIsLoading(false);
      setIsLoadingMore(false);
    }
  }, []);

  // Select a conversation and fetch its details
  const selectConversation = useCallback(async (convId: number) => {
    try {
      setIsLoading(true);

      // Clear previous conversation state
      setMessages([]);
      setTypingUsers({});
      setCurrentPage(1);
      setHasMore(true);

      console.log(`Selecting conversation ${convId}`);
      const conversation = await messagesService.getConversationById(convId);
      console.log('Conversation loaded:', conversation);

      setCurrentConversation(conversation);

      // Fetch messages for the new conversation
      await fetchMessages(convId, 1, true);
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to select conversation';
      toast.error(message);
      console.error('Error selecting conversation:', error);
    } finally {
      setIsLoading(false);
    }
  }, [fetchMessages]);

  // Load more messages (older messages)
  const loadMoreMessages = useCallback(() => {
    if (currentConversation && !isLoadingMore && hasMore) {
      fetchMessages(currentConversation.id, currentPage + 1);
    }
  }, [currentConversation, fetchMessages, currentPage, isLoadingMore, hasMore]);

  // Send message
  const sendMessage = useCallback(async (content: string, convId?: number): Promise<void> => {
    const targetConversationId = convId || currentConversation?.id;
    if (!targetConversationId || !user) return;

    console.log('useMessages sendMessage - user ID debug:', {
      user_id: user.id,
      user_id_type: typeof user.id,
      user_object: user
    });

    const tempId = `temp_${Date.now()}_${Math.random()}`;
    let tempMessage: Message | null = null;

    try {
      // Create temporary message for optimistic update
      tempMessage = {
        id: Date.now(), // Temporary ID
        conversation_id: targetConversationId,
        user_id: user.id,
        sender_id: user.id,
        sender: user,
        type: 'text',
        content,
        is_read: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        temp_id: tempId,
      };

      console.log('Adding temporary message:', tempMessage);
      // Add temporary message to UI
      setMessages(prev => [...prev, tempMessage as Message]);

      // Send message via API
      const actualMessage = await messagesService.sendMessage(targetConversationId, content);
      console.log('Received actual message from API:', actualMessage);

      // Replace temporary message with actual message from API response
      setMessages(prev => prev.map(msg => {
        if ((msg as any).temp_id === tempId) {
          console.log('Replacing temp message with actual message');
          return actualMessage;
        }
        return msg;
      }));

      // Update conversation list
      setConversations(prev => prev.map(conv => {
        if (conv.id === targetConversationId) {
          return {
            ...conv,
            last_message: actualMessage,
            updated_at: actualMessage.created_at,
          };
        }
        return conv;
      }));

    } catch (error: any) {
      // Remove temporary message on error
      if (tempMessage) {
        console.log('Removing temporary message due to error');
        setMessages(prev => prev.filter(msg => (msg as any).temp_id !== tempId));
      }
      const message = error.response?.data?.message || 'Failed to send message';
      toast.error(message);
    }
  }, [currentConversation, user]);

  // Send media message
  const sendMediaMessage = useCallback(async (file: File, convId?: number, onProgress?: (progress: number) => void): Promise<void> => {
    const targetConversationId = convId || currentConversation?.id;
    if (!targetConversationId) return;

    try {
      const message = await messagesService.sendMediaMessage(targetConversationId, file, onProgress);
      setMessages(prev => [...prev, message]);

      // Update conversation list
      setConversations(prev => prev.map(conv => {
        if (conv.id === targetConversationId) {
          return {
            ...conv,
            last_message: message,
            updated_at: message.created_at,
          };
        }
        return conv;
      }));
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to send media';
      toast.error(message);
    }
  }, [currentConversation]);

  // Start or get conversation with user
  const startConversation = useCallback(async (username: string): Promise<Conversation> => {
    try {
      const conversation = await messagesService.getOrCreateConversation(username);
      setCurrentConversation(conversation);

      // Add to conversations list if not exists
      setConversations(prev => {
        const exists = prev.find(conv => conv.id === conversation.id);
        if (!exists) {
          return [conversation, ...prev];
        }
        return prev;
      });

      return conversation;
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to start conversation';
      toast.error(message);
      throw error;
    }
  }, []);

  // Mark messages as read
  const markAsRead = useCallback(async (convId?: number): Promise<void> => {
    const targetConversationId = convId || currentConversation?.id;
    if (!targetConversationId) return;

    try {
      await messagesService.markAsRead(targetConversationId);

      // Update messages locally
      setMessages(prev => prev.map(msg => ({
        ...msg,
        is_read: true
      })));

      // Send via socket
      socketService.markAsRead(targetConversationId, []);

    } catch (error: any) {
      console.error('Failed to mark messages as read:', error);
    }
  }, [currentConversation]);

  // Send typing indicator
  const sendTyping = useCallback((isTyping: boolean, convId?: number) => {
    const targetConversationId = convId || currentConversation?.id;
    if (!targetConversationId) return;

    socketService.sendTyping(targetConversationId, isTyping);

    // Clear previous timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Auto-stop typing after 3 seconds
    if (isTyping) {
      typingTimeoutRef.current = setTimeout(() => {
        socketService.sendTyping(targetConversationId, false);
      }, 3000);
    }
  }, [currentConversation]);

  // Get unread count
  const fetchUnreadCount = useCallback(async () => {
    try {
      const result = await messagesService.getUnreadCount();
      setUnreadCount(result.count);
    } catch (error) {
      console.error('Failed to fetch unread count:', error);
    }
  }, []);

  // Scroll to bottom
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  // Socket event handlers
  useEffect(() => {
    if (!autoConnect) return;

    const handleNewMessage = (message: Message) => {
      console.log('useMessages received new_message:', message);
      console.log('Current conversation ID:', currentConversation?.id);
      console.log('Message debug info:', {
        message_user_id: message.user_id,
        message_user_id_type: typeof message.user_id,
        current_user_id: user?.id,
        current_user_id_type: typeof user?.id,
        equals_strict: message.user_id === user?.id,
        equals_loose: message.user_id == user?.id,
        number_comparison: Number(message.user_id) === Number(user?.id)
      });

      // Skip if this message is from current user AND we already have it (to avoid duplicates from optimistic updates)
      if (Number(message.user_id) === Number(user?.id)) {
        console.log('Message from current user - checking if already exists');

        // Check if we already have this message (by ID or temp_id)
        const existsInCurrentMessages = messages.find(msg =>
          msg.id === message.id ||
          (message.temp_id && (msg as any).temp_id === message.temp_id)
        );

        if (existsInCurrentMessages) {
          console.log('Message already exists, skipping to avoid duplicate');
          return;
        } else {
          console.log('Message from current user but not in current messages, adding it');
        }
      }

      // Only add message if it belongs to the active conversation and is from another user
      if (message.conversation_id === currentConversation?.id) {
        setMessages(prev => {
          // Avoid duplicates by checking both ID and temp_id
          const exists = prev.find(msg =>
            msg.id === message.id ||
            (message.temp_id && (msg as any).temp_id === message.temp_id)
          );
          if (exists) {
            console.log('Message already exists, skipping');
            return prev;
          }
          console.log('Adding new message from another user to conversation');
          return [...prev, message];
        });
      } else {
        console.log('Message not for current conversation, updating conversation list only');
      }

      // Update conversation list preview
      setConversations(prev => prev.map(conv => {
        if (conv.id === message.conversation_id) {
          return {
            ...conv,
            last_message: message,
            updated_at: message.created_at,
            unread_count: Number(message.user_id) !== Number(user?.id) ? (conv.unread_count || 0) + 1 : conv.unread_count,
          };
        }
        return conv;
      }));

      // Update total unread count
      if (Number(message.user_id) !== Number(user?.id)) {
        setUnreadCount(prev => prev + 1);
      }
    };

    const handleUserTyping = (data: { conversation_id: number; user: User; is_typing: boolean }) => {
      if (Number(data.user.id) !== Number(user?.id)) {
        setTypingUsers(prev => {
          const conversationTyping = prev[data.conversation_id] || [];

          if (data.is_typing) {
            // Add user to typing list if not already there
            const isAlreadyTyping = conversationTyping.find(u => u.id === data.user.id);
            if (!isAlreadyTyping) {
              return {
                ...prev,
                [data.conversation_id]: [...conversationTyping, data.user]
              };
            }
          } else {
            // Remove user from typing list
            const filteredTyping = conversationTyping.filter(u => u.id !== data.user.id);
            return {
              ...prev,
              [data.conversation_id]: filteredTyping
            };
          }

          return prev;
        });
      }
    };

    const handleUserOnline = (data: { user_id: number; is_online: boolean }) => {
      setOnlineUsers(prev => {
        const newSet = new Set(prev);
        if (data.is_online) {
          newSet.add(data.user_id);
        } else {
          newSet.delete(data.user_id);
        }
        return newSet;
      });
    };

    const handleMessageRead = (data: { conversation_id: number; message_ids: number[]; user_id: number }) => {
      if (data.conversation_id === currentConversation?.id && Number(data.user_id) !== Number(user?.id)) {
        setMessages(prev => prev.map(msg => {
          if (data.message_ids.includes(msg.id)) {
            return { ...msg, is_read: true };
          }
          return msg;
        }));
      }
    };

    socketService.on('new_message', handleNewMessage);
    socketService.on('user_typing', handleUserTyping);
    socketService.on('user_online', handleUserOnline);
    socketService.on('message_read', handleMessageRead);

    return () => {
      socketService.off('new_message', handleNewMessage);
      socketService.off('user_typing', handleUserTyping);
      socketService.off('user_online', handleUserOnline);
      socketService.off('message_read', handleMessageRead);
    };
  }, [currentConversation?.id, user?.id, autoConnect]);

  // Join/leave conversation room
  useEffect(() => {
    if (currentConversation?.id && autoConnect) {
      socketService.joinConversation(currentConversation.id);

      return () => {
        socketService.leaveConversation(currentConversation.id);
      };
    }
  }, [currentConversation?.id, autoConnect]);

  // Fetch initial data
  useEffect(() => {
    if (autoConnect) {
      fetchConversations();
      fetchUnreadCount();
    }
  }, [fetchConversations, fetchUnreadCount, autoConnect]);

  // Fetch messages when conversation changes
  useEffect(() => {
    if (currentConversation?.id) {
      fetchMessages(currentConversation.id, 1, true);
      setCurrentPage(1);
      setHasMore(true);
    }
  }, [currentConversation?.id, fetchMessages]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    scrollToBottom();
  }, [messages.length, scrollToBottom]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Clear typing timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, []);

  return {
    conversations,
    currentConversation,
    messages,
    isLoading,
    isLoadingMore,
    hasMore,
    typingUsers,
    onlineUsers,
    unreadCount,
    messagesEndRef,
    fetchConversations,
    fetchMessages,
    loadMoreMessages,
    selectConversation,
    sendMessage,
    sendMediaMessage,
    startConversation,
    markAsRead,
    sendTyping,
    scrollToBottom,
    setCurrentConversation,
  };
};

export default useMessages;