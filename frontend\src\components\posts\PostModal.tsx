import React, { useState } from "react";
import type { Post } from "../../types";
import {
  XMarkIcon,
  HeartIcon,
  ChatBubbleOvalLeftIcon,
  PaperAirplaneIcon,
  BookmarkIcon,
  EllipsisHorizontalIcon,
} from "@heroicons/react/24/outline";
import {
  HeartIcon as HeartIconSolid,
  BookmarkIcon as BookmarkIconSolid,
} from "@heroicons/react/24/solid";
import { formatDistanceToNow } from "date-fns";
import { vi } from "date-fns/locale";
import CommentSection from "./CommentSection";

interface PostModalProps {
  post: Post;
  onClose: () => void;
  onLike: () => void;
  onSave: () => void;
}

const PostModal: React.FC<PostModalProps> = ({
  post,
  onClose,
  onLike,
  onSave,
}) => {
  console.log("check post", post);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  const handleImageNavigation = (direction: "prev" | "next") => {
    if (direction === "prev" && currentImageIndex > 0) {
      setCurrentImageIndex(currentImageIndex - 1);
    } else if (
      direction === "next" &&
      currentImageIndex < post.media.length - 1
    ) {
      setCurrentImageIndex(currentImageIndex + 1);
    }
  };

  const formatTimeAgo = (date: string) => {
    return formatDistanceToNow(new Date(date), {
      addSuffix: true,
      locale: vi,
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-hidden flex">
        {/* Media Section */}
        <div className="flex-1 bg-black flex items-center justify-center relative">
          {post.media.length > 0 && (
            <>
              <img
                src={post.media[currentImageIndex].file_url}
                alt="Post media"
                className="max-w-full max-h-full object-contain"
              />

              {/* Image Navigation */}
              {post.media.length > 1 && (
                <>
                  {currentImageIndex > 0 && (
                    <button
                      onClick={() => handleImageNavigation("prev")}
                      className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white rounded-full p-2 hover:bg-opacity-70"
                    >
                      <svg
                        className="w-6 h-6"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M15 19l-7-7 7-7"
                        />
                      </svg>
                    </button>
                  )}

                  {currentImageIndex < post.media.length - 1 && (
                    <button
                      onClick={() => handleImageNavigation("next")}
                      className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white rounded-full p-2 hover:bg-opacity-70"
                    >
                      <svg
                        className="w-6 h-6"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 5l7 7-7 7"
                        />
                      </svg>
                    </button>
                  )}

                  {/* Dots indicator */}
                  <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                    {post.media.map((_, index) => (
                      <div
                        key={index}
                        className={`w-2 h-2 rounded-full ${
                          index === currentImageIndex
                            ? "bg-white"
                            : "bg-white bg-opacity-50"
                        }`}
                      />
                    ))}
                  </div>
                </>
              )}
            </>
          )}
        </div>

        {/* Content Section */}
        <div className="w-96 flex flex-col">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <img
                src={
                  post.user.avatar ||
                  "https://api-private.atlassian.com/users/8b3597e8a7d1d8f2ed7f58bcab1946b8/avatar"
                }
                alt={post.user.username}
                className="w-8 h-8 rounded-full object-cover"
              />
              <div>
                <p className="font-semibold text-sm text-gray-900">
                  {post.user.username}
                </p>
                {post.location && (
                  <p className="text-xs text-gray-500">{post.location}</p>
                )}
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <button className="p-1 hover:bg-gray-100 rounded-full">
                <EllipsisHorizontalIcon className="w-5 h-5 text-gray-600" />
              </button>
              <button
                onClick={onClose}
                className="p-1 hover:bg-gray-100 rounded-full"
              >
                <XMarkIcon className="w-5 h-5 text-gray-600" />
              </button>
            </div>
          </div>

          {/* Caption and Comments */}
          <div className="flex-1 overflow-y-auto">
            {/* Caption */}
            {post.caption && (
              <div className="p-4 border-b border-gray-200">
                <div className="flex space-x-3">
                  <img
                    src={
                      post.user.avatar ||
                      "https://api-private.atlassian.com/users/8b3597e8a7d1d8f2ed7f58bcab1946b8/avatar"
                    }
                    alt={post.user.username}
                    className="w-8 h-8 rounded-full object-cover"
                  />
                  <div>
                    <span className="font-semibold text-sm text-gray-900 mr-2">
                      {post.user.username}
                    </span>
                    <span className="text-sm text-gray-900">
                      {post.caption}
                    </span>
                    <p className="text-xs text-gray-400 mt-1">
                      {formatTimeAgo(post.created_at)}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Comments */}
            <CommentSection postId={post.id} postOwnerId={post.user_id} />
          </div>

          {/* Actions */}
          <div className="border-t border-gray-200 p-4">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-4">
                <button
                  onClick={onLike}
                  className="hover:opacity-70 transition-opacity"
                >
                  {post.is_liked ? (
                    <HeartIconSolid className="w-6 h-6 text-red-500" />
                  ) : (
                    <HeartIcon className="w-6 h-6 text-gray-900" />
                  )}
                </button>

                <button className="hover:opacity-70 transition-opacity">
                  <ChatBubbleOvalLeftIcon className="w-6 h-6 text-gray-900" />
                </button>
              </div>
            </div>

            {/* Likes */}
            {post.likes_count > 0 && (
              <p className="font-semibold text-sm text-gray-900 mb-2">
                {post.likes_count} lượt thích
              </p>
            )}

            {/* Time */}
            <p className="text-xs text-gray-400 uppercase">
              {formatTimeAgo(post.created_at)}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PostModal;
