import axios, { type AxiosInstance, type AxiosResponse } from 'axios';
import type { ApiResponse, PaginatedResponse, Comment } from '../types';

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: 'http://localhost:8001/api',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor to handle errors
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          // Token expired or invalid
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // Generic methods
  async get<T = unknown>(url: string, params?: Record<string, unknown>): Promise<ApiResponse<T>> {
    const response: AxiosResponse<ApiResponse<T>> = await this.api.get(url, { params });
    return response.data;
  }

  async post<T = unknown>(url: string, data?: Record<string, unknown> | FormData): Promise<ApiResponse<T>> {
    const response: AxiosResponse<ApiResponse<T>> = await this.api.post(url, data);
    return response.data;
  }

  async put<T = unknown>(url: string, data?: Record<string, unknown> | FormData): Promise<ApiResponse<T>> {
    const response: AxiosResponse<ApiResponse<T>> = await this.api.put(url, data);
    return response.data;
  }

  async delete<T = unknown>(url: string): Promise<ApiResponse<T>> {
    const response: AxiosResponse<ApiResponse<T>> = await this.api.delete(url);
    return response.data;
  }

  async patch<T = unknown>(url: string, data?: Record<string, unknown> | FormData): Promise<ApiResponse<T>> {
    const response: AxiosResponse<ApiResponse<T>> = await this.api.patch(url, data);
    return response.data;
  }

  async getPaginated<T = unknown>(url: string, params?: Record<string, unknown>): Promise<PaginatedResponse<T>> {
    const response: AxiosResponse<ApiResponse<PaginatedResponse<T>>> = await this.api.get(url, { params });
    console.log('API getPaginated response:', response.data);
    return response.data.data;
  }

  // File upload method
  async upload<T = unknown>(url: string, formData: FormData, onProgress?: (progress: number) => void): Promise<ApiResponse<T>> {
    const response: AxiosResponse<ApiResponse<T>> = await this.api.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    });
    return response.data;
  }

  // Set auth token
  setAuthToken(token: string) {
    localStorage.setItem('token', token);
    this.api.defaults.headers.Authorization = `Bearer ${token}`;
  }

  // Remove auth token
  removeAuthToken() {
    localStorage.removeItem('token');
    delete this.api.defaults.headers.Authorization;
  }

  // Get current auth token
  getAuthToken(): string | null {
    return localStorage.getItem('token');
  }

  // Comments API methods
  async getComments(postId: string, page: number = 1): Promise<PaginatedResponse<Comment>> {
    return this.getPaginated(`/posts/${postId}/comments`, { page });
  }

  async createComment(postId: string, content: string, parentId?: string): Promise<ApiResponse<Comment>> {
    return this.post(`/posts/${postId}/comments`, { content, parent_id: parentId });
  }

  async updateComment(commentId: string, content: string): Promise<ApiResponse<Comment>> {
    return this.put(`/comments/${commentId}`, { content });
  }

  async deleteComment(commentId: string): Promise<ApiResponse<{ success: boolean }>> {
    return this.delete(`/comments/${commentId}`);
  }

  async likeComment(commentId: string): Promise<ApiResponse<{ liked: boolean; likes_count: number }>> {
    return this.post(`/comments/${commentId}/like`);
  }

  async unlikeComment(commentId: string): Promise<ApiResponse<{ liked: boolean; likes_count: number }>> {
    return this.delete(`/comments/${commentId}/like`);
  }

  async getCommentReplies(commentId: string, page: number = 1): Promise<PaginatedResponse<Comment>> {
    return this.getPaginated(`/comments/${commentId}/replies`, { page });
  }
}

export const apiService = new ApiService();
export default apiService;