import React, { useState } from "react";
import { Link } from "react-router-dom";
import type { Post } from "../../types";
import { HeartIcon, ChatBubbleOvalLeftIcon } from "@heroicons/react/24/solid";
import PostModal from "./PostModal";

interface ProfilePostCardProps {
  post: Post;
  onLike: () => void;
  onSave: () => void;
}

const ProfilePostCard: React.FC<ProfilePostCardProps> = ({
  post,
  onLike,
  onSave,
}) => {
  const [showModal, setShowModal] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  return (
    <>
      <div
        className="relative aspect-square bg-gray-100 cursor-pointer group overflow-hidden"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onClick={() => setShowModal(true)}
      >
        {/* Main Image */}
        {post.media.length > 0 && (
          <img
            src={post.media[0]?.file_url}
            alt="Post"
            className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
          />
        )}

        {/* Multiple Images Indicator */}
        {post.media.length > 1 && (
          <div className="absolute top-2 right-2">
            <svg
              className="w-5 h-5 text-white drop-shadow-lg"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
                clipRule="evenodd"
              />
              <path
                fillRule="evenodd"
                d="M15 7a1 1 0 100-2 1 1 0 000 2z"
                clipRule="evenodd"
              />
            </svg>
          </div>
        )}

        {/* Video Indicator */}
        {post.media[0]?.type === "video" && (
          <div className="absolute top-2 right-2">
            <svg
              className="w-5 h-5 text-white drop-shadow-lg"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"
                clipRule="evenodd"
              />
            </svg>
          </div>
        )}

        {/* Hover Overlay */}
        {isHovered && (
          <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center transition-opacity duration-300">
            <div className="flex items-center space-x-6 text-white">
              <div className="flex items-center space-x-2">
                <HeartIcon className="w-6 h-6" />
                <span className="font-semibold text-lg">
                  {post.likes_count || 0}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <ChatBubbleOvalLeftIcon className="w-6 h-6" />
                <span className="font-semibold text-lg">
                  {post.comments_count || 0}
                </span>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Post Modal */}
      {showModal && (
        <PostModal
          post={post}
          onClose={() => setShowModal(false)}
          onLike={onLike}
          onSave={onSave}
        />
      )}
    </>
  );
};

export default ProfilePostCard;
