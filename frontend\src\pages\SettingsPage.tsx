import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";
import LoadingSpinner from "../components/ui/LoadingSpinner";
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  UserIcon,
  LockClosedIcon,
  BellIcon,
  EyeSlashIcon,
  ShieldCheckIcon,
  QuestionMarkCircleIcon,
  ExclamationTriangleIcon,
  ArrowRightOnRectangleIcon,
  MoonIcon,
  SunIcon,
  GlobeAltIcon,
  DevicePhoneMobileIcon,
} from "@heroicons/react/24/outline";
import { toast } from "react-hot-toast";

interface SettingItem {
  id: string;
  title: string;
  description?: string;
  icon: React.ComponentType<{ className?: string }>;
  action: () => void;
  danger?: boolean;
  toggle?: boolean;
  enabled?: boolean;
}

interface SettingSection {
  title: string;
  items: SettingItem[];
}

const SettingsPage: React.FC = () => {
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const [loading, setLoading] = useState(false);
  const [darkMode, setDarkMode] = useState(false);
  const [notifications, setNotifications] = useState(true);
  const [privateAccount, setPrivateAccount] = useState(false);
  const [showOnlineStatus, setShowOnlineStatus] = useState(true);

  const handleLogout = async () => {
    try {
      setLoading(true);
      await logout();
      toast.success("Đã đăng xuất thành công");
      navigate("/login");
    } catch (error) {
      console.error("Error logging out:", error);
      toast.error("Không thể đăng xuất");
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteAccount = () => {
    const confirmed = window.confirm(
      "Bạn có chắc chắn muốn xóa tài khoản? Hành động này không thể hoàn tác."
    );

    if (confirmed) {
      toast.error("Tính năng xóa tài khoản đang được phát triển");
    }
  };

  const settingSections: SettingSection[] = [
    {
      title: "Tài khoản",
      items: [
        {
          id: "edit-profile",
          title: "Chỉnh sửa trang cá nhân",
          description: "Thay đổi ảnh, tên, tiểu sử",
          icon: UserIcon,
          action: () => navigate("/accounts/edit"),
        },
        {
          id: "change-password",
          title: "Đổi mật khẩu",
          description: "Cập nhật mật khẩu của bạn",
          icon: LockClosedIcon,
          action: () => toast.success("Tính năng đang được phát triển"),
        },
        {
          id: "two-factor",
          title: "Xác thực hai yếu tố",
          description: "Tăng cường bảo mật tài khoản",
          icon: ShieldCheckIcon,
          action: () => toast.success("Tính năng đang được phát triển"),
        },
      ],
    },
    {
      title: "Riêng tư và bảo mật",
      items: [
        {
          id: "private-account",
          title: "Tài khoản riêng tư",
          description: "Chỉ người theo dõi mới xem được bài viết",
          icon: EyeSlashIcon,
          toggle: true,
          enabled: privateAccount,
          action: () => {
            setPrivateAccount(!privateAccount);
            toast.success(
              privateAccount
                ? "Đã tắt chế độ riêng tư"
                : "Đã bật chế độ riêng tư"
            );
          },
        },
        {
          id: "online-status",
          title: "Trạng thái hoạt động",
          description: "Hiển thị khi bạn đang online",
          icon: DevicePhoneMobileIcon,
          toggle: true,
          enabled: showOnlineStatus,
          action: () => {
            setShowOnlineStatus(!showOnlineStatus);
            toast.success(
              showOnlineStatus
                ? "Đã ẩn trạng thái hoạt động"
                : "Đã hiện trạng thái hoạt động"
            );
          },
        },
        {
          id: "blocked-accounts",
          title: "Tài khoản bị chặn",
          description: "Quản lý danh sách chặn",
          icon: ExclamationTriangleIcon,
          action: () => toast("Tính năng đang được phát triển"),
        },
      ],
    },
    {
      title: "Thông báo",
      items: [
        {
          id: "push-notifications",
          title: "Thông báo đẩy",
          description: "Nhận thông báo về hoạt động",
          icon: BellIcon,
          toggle: true,
          enabled: notifications,
          action: () => {
            setNotifications(!notifications);
            toast.success(
              notifications ? "Đã tắt thông báo" : "Đã bật thông báo"
            );
          },
        },
        {
          id: "email-notifications",
          title: "Thông báo email",
          description: "Nhận email về hoạt động quan trọng",
          icon: BellIcon,
          action: () => toast("Tính năng đang được phát triển"),
        },
      ],
    },
    {
      title: "Giao diện",
      items: [
        {
          id: "dark-mode",
          title: "Chế độ tối",
          description: "Sử dụng giao diện tối",
          icon: darkMode ? MoonIcon : SunIcon,
          toggle: true,
          enabled: darkMode,
          action: () => {
            setDarkMode(!darkMode);
            toast.success(
              darkMode
                ? "Đã chuyển sang chế độ sáng"
                : "Đã chuyển sang chế độ tối"
            );
          },
        },
        {
          id: "language",
          title: "Ngôn ngữ",
          description: "Tiếng Việt",
          icon: GlobeAltIcon,
          action: () => toast("Tính năng đang được phát triển"),
        },
      ],
    },
    {
      title: "Hỗ trợ",
      items: [
        {
          id: "help",
          title: "Trung tâm trợ giúp",
          description: "Tìm câu trả lời cho câu hỏi của bạn",
          icon: QuestionMarkCircleIcon,
          action: () => window.open("https://help.instagram.com", "_blank"),
        },
        {
          id: "report-problem",
          title: "Báo cáo sự cố",
          description: "Cho chúng tôi biết về vấn đề bạn gặp phải",
          icon: ExclamationTriangleIcon,
          action: () => toast("Tính năng đang được phát triển"),
        },
      ],
    },
    {
      title: "Đăng xuất",
      items: [
        {
          id: "logout",
          title: "Đăng xuất",
          description: "Đăng xuất khỏi tài khoản này",
          icon: ArrowRightOnRectangleIcon,
          action: handleLogout,
        },
        {
          id: "delete-account",
          title: "Xóa tài khoản",
          description: "Xóa vĩnh viễn tài khoản của bạn",
          icon: ExclamationTriangleIcon,
          danger: true,
          action: handleDeleteAccount,
        },
      ],
    },
  ];

  const renderSettingItem = (item: SettingItem) => {
    const IconComponent = item.icon;

    return (
      <button
        key={item.id}
        onClick={item.action}
        disabled={loading && item.id === "logout"}
        className={`w-full flex items-center justify-between p-4 hover:bg-gray-50 transition-colors ${
          item.danger ? "text-red-600" : "text-gray-900"
        }`}
      >
        <div className="flex items-center space-x-3">
          <IconComponent className="w-6 h-6" />
          <div className="text-left">
            <div className="font-medium">{item.title}</div>
            {item.description && (
              <div
                className={`text-sm ${
                  item.danger ? "text-red-500" : "text-gray-500"
                }`}
              >
                {item.description}
              </div>
            )}
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {loading && item.id === "logout" ? (
            <LoadingSpinner size="sm" />
          ) : item.toggle ? (
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={item.enabled}
                onChange={item.action}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          ) : (
            <ChevronRightIcon className="w-5 h-5 text-gray-400" />
          )}
        </div>
      </button>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-4 py-3 sticky top-0 z-10">
        <div className="flex items-center space-x-4 max-w-2xl mx-auto">
          <button
            onClick={() => navigate(-1)}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <ChevronLeftIcon className="w-6 h-6" />
          </button>
          <h1 className="text-xl font-semibold">Cài đặt</h1>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-2xl mx-auto">
        {/* User Info */}
        <div className="bg-white border-b border-gray-200 p-6">
          <div className="flex items-center space-x-4">
            <img
              src={user?.avatar || "https://api-private.atlassian.com/users/8b3597e8a7d1d8f2ed7f58bcab1946b8/avatar"}
              alt={user?.username}
              className="w-16 h-16 rounded-full object-cover"
            />
            <div>
              <h2 className="text-lg font-semibold">{user?.full_name}</h2>
              <p className="text-gray-600">@{user?.username}</p>
              {user?.email && (
                <p className="text-sm text-gray-500">{user.email}</p>
              )}
            </div>
          </div>
        </div>

        {/* Settings Sections */}
        <div className="space-y-1">
          {settingSections.map((section, sectionIndex) => (
            <div key={sectionIndex} className="bg-white">
              {section.title && (
                <div className="px-4 py-3 border-b border-gray-100">
                  <h3 className="text-sm font-semibold text-gray-500 uppercase tracking-wide">
                    {section.title}
                  </h3>
                </div>
              )}
              <div className="divide-y divide-gray-100">
                {section.items.map(renderSettingItem)}
              </div>
            </div>
          ))}
        </div>

        {/* App Info */}
        <div className="bg-white mt-1 p-6 text-center">
          <div className="text-sm text-gray-500 space-y-1">
            <p>Instagram Clone v1.0.0</p>
            <p>© 2024 Instagram Clone. All rights reserved.</p>
            <div className="flex justify-center space-x-4 mt-4">
              <button className="text-blue-500 hover:underline">
                Điều khoản sử dụng
              </button>
              <button className="text-blue-500 hover:underline">
                Chính sách bảo mật
              </button>
              <button className="text-blue-500 hover:underline">Liên hệ</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsPage;
