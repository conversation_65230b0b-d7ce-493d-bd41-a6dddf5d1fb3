<?php

namespace App\Http\Controllers;

use App\Models\Hashtag;
use App\Models\Post;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class HashtagController extends Controller
{
    public function index(Request $request)
    {
        $hashtags = Hashtag::popular()
            ->select(['id', 'name', 'slug', 'post_count'])
            ->paginate(20);

        return response()->json([
            'success' => true,
            'data' => $hashtags
        ]);
    }

    public function show(Request $request, $slug)
    {
        $hashtag = Hashtag::where('slug', $slug)->firstOrFail();

        $posts = $hashtag->posts()
            ->with(['user:id,username,name,avatar', 'media', 'hashtags:id,name'])
            ->withCount(['likes', 'comments'])
            ->latest()
            ->paginate(12);

        // Add like status for authenticated user
        if ($request->user()) {
            $posts->getCollection()->transform(function ($post) use ($request) {
                $post->is_liked = $post->isLikedBy($request->user());
                return $post;
            });
        }

        return response()->json([
            'success' => true,
            'data' => [
                'hashtag' => $hashtag,
                'posts' => $posts
            ]
        ]);
    }

    public function search(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'q' => 'required|string|min:1',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $query = $request->q;

        $hashtags = Hashtag::where('name', 'like', "%{$query}%")
            ->orWhere('slug', 'like', "%{$query}%")
            ->select(['id', 'name', 'slug', 'posts_count'])
            ->limit(20)
            ->get();

        return response()->json([
            'success' => true,
            'data' => $hashtags
        ]);
    }

    public function trending(Request $request)
    {
        // Get hashtags that have been used in posts from the last 7 days
        $hashtags = Hashtag::whereHas('posts', function ($query) {
            $query->where('created_at', '>=', now()->subDays(7));
        })
            ->withCount(['posts as recent_post_count' => function ($query) {
                $query->where('created_at', '>=', now()->subDays(7));
            }])
            ->orderBy('recent_post_count', 'desc')
            ->select(['id', 'name', 'slug', 'posts_count'])
            ->limit(10)
            ->get();

        return response()->json([
            'success' => true,
            'data' => $hashtags
        ]);
    }

    public function related(Request $request, $slug)
    {
        $hashtag = Hashtag::where('slug', $slug)->firstOrFail();

        // Find hashtags that appear together with this hashtag in posts
        $relatedHashtags = Hashtag::whereHas('posts', function ($query) use ($hashtag) {
            $query->whereHas('hashtags', function ($q) use ($hashtag) {
                $q->where('hashtags.id', $hashtag->id);
            });
        })
            ->where('id', '!=', $hashtag->id)
            ->withCount(['posts as common_post_count' => function ($query) use ($hashtag) {
                $query->whereHas('hashtags', function ($q) use ($hashtag) {
                    $q->where('hashtags.id', $hashtag->id);
                });
            }])
            ->orderBy('common_post_count', 'desc')
            ->select(['id', 'name', 'slug', 'posts_count'])
            ->limit(10)
            ->get();

        return response()->json([
            'success' => true,
            'data' => $relatedHashtags
        ]);
    }
}
