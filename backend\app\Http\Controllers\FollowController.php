<?php

namespace App\Http\Controllers;

use App\Models\Follow;
use App\Models\User;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class FollowController extends Controller
{
    public function follow(Request $request, $username)
    {
        $userToFollow = User::where('username', $username)->firstOrFail();
        $currentUser = $request->user();

        // Can't follow yourself
        if ($userToFollow->id === $currentUser->id) {
            return response()->json([
                'success' => false,
                'message' => 'You cannot follow yourself'
            ], 400);
        }

        // Check if already following
        $existingFollow = Follow::where('follower_id', $currentUser->id)
            ->where('following_id', $userToFollow->id)
            ->first();

        if ($existingFollow) {
            return response()->json([
                'success' => false,
                'message' => 'Already following this user'
            ], 400);
        }

        // Always create follow request (pending status) regardless of privacy
        $status = 'pending';

        $follow = Follow::create([
            'follower_id' => $currentUser->id,
            'following_id' => $userToFollow->id,
            'status' => $status,
        ]);

        // Create notification
        if ($status === 'pending') {
            NotificationService::createFollowRequestNotification($currentUser, $userToFollow);
        } else {
            NotificationService::createFollowNotification($currentUser, $userToFollow);
        }

        $message = $status === 'pending' ? 'Follow request sent' : 'Now following user';

        // Get updated followers count reliably
        $followersCount = Follow::where('following_id', $userToFollow->id)->where('status', 'accepted')->count();

        return response()->json([
            'success' => true,
            'message' => $message,
            'is_following' => $status === 'accepted',
            'status' => $status,
            'followers_count' => $followersCount,
            'data' => [
                'status' => $status,
                'is_following' => $status === 'accepted'
            ]
        ]);
    }

    public function unfollow(Request $request, $username)
    {
        $userToUnfollow = User::where('username', $username)->firstOrFail();
        $currentUser = $request->user();

        $follow = Follow::where('follower_id', $currentUser->id)
            ->where('following_id', $userToUnfollow->id)
            ->first();

        if (!$follow) {
            return response()->json([
                'success' => false,
                'message' => 'Not following this user'
            ], 400);
        }

        // Remove notifications
        if ($follow->status === 'pending') {
            NotificationService::removeFollowRequestNotification($currentUser, $userToUnfollow);
        } else {
            NotificationService::removeFollowNotification($currentUser, $userToUnfollow);
        }

        $follow->delete();

        // Get updated followers count reliably
        $followersCount = Follow::where('following_id', $userToUnfollow->id)->where('status', 'accepted')->count();

        return response()->json([
            'success' => true,
            'message' => 'Unfollowed successfully',
            'is_following' => false,
            'follow_status' => null,
            'followers_count' => $followersCount,
            'data' => [
                'is_following' => false
            ]
        ]);
    }

    public function getFollowRequestId(Request $request, $followerId)
    {
        $follow = Follow::where('follower_id', $followerId)
                       ->where('following_id', $request->user()->id)
                       ->where('status', 'pending')
                       ->first();

        if (!$follow) {
            return response()->json([
                'success' => false,
                'message' => 'Follow request not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'follow_request_id' => $follow->id
        ]);
    }

    public function acceptFollowRequest(Request $request, $followId)
    {
        $follow = Follow::findOrFail($followId);

        // Check if current user is the one being followed
        if ($follow->following_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        if ($follow->status !== 'pending') {
            return response()->json([
                'success' => false,
                'message' => 'Follow request is not pending'
            ], 400);
        }

        $follow->accept();

        // Remove follow request notification and create follow notification
        NotificationService::removeFollowRequestNotification($follow->follower, $follow->following);
        NotificationService::createFollowNotification($follow->follower, $follow->following);

        return response()->json([
            'success' => true,
            'message' => 'Follow request accepted'
        ]);
    }

    public function rejectFollowRequest(Request $request, $followId)
    {
        $follow = Follow::findOrFail($followId);

        // Check if current user is the one being followed
        if ($follow->following_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        if ($follow->status !== 'pending') {
            return response()->json([
                'success' => false,
                'message' => 'Follow request is not pending'
            ], 400);
        }

        // Remove follow request notification
        NotificationService::removeFollowRequestNotification($follow->follower, $follow->following);
        
        $follow->delete();

        return response()->json([
            'success' => true,
            'message' => 'Follow request rejected'
        ]);
    }

    public function pendingRequests(Request $request)
    {
        $currentUser = $request->user();

        $pendingRequests = Follow::where('following_id', $currentUser->id)
            ->where('status', 'pending')
            ->with(['follower'])
            ->latest()
            ->paginate(20);

        return response()->json([
            'success' => true,
            'data' => $pendingRequests
        ]);
    }

    public function sentRequests(Request $request)
    {
        $currentUser = $request->user();

        $sentRequests = Follow::where('follower_id', $currentUser->id)
            ->where('status', 'pending')
            ->with(['following'])
            ->latest()
            ->paginate(20);

        return response()->json([
            'success' => true,
            'data' => $sentRequests
        ]);
    }

    public function cancelRequest(Request $request, $followId)
    {
        $follow = Follow::findOrFail($followId);

        // Check if current user sent this request
        if ($follow->follower_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        if ($follow->status !== 'pending') {
            return response()->json([
                'success' => false,
                'message' => 'Follow request is not pending'
            ], 400);
        }

        $follow->delete();

        return response()->json([
            'success' => true,
            'message' => 'Follow request cancelled'
        ]);
    }

    public function removeFollower(Request $request, $username)
    {
        $follower = User::where('username', $username)->firstOrFail();
        $currentUser = $request->user();

        $follow = Follow::where('follower_id', $follower->id)
            ->where('following_id', $currentUser->id)
            ->where('status', 'accepted')
            ->first();

        if (!$follow) {
            return response()->json([
                'success' => false,
                'message' => 'User is not following you'
            ], 400);
        }

        $follow->delete();

        return response()->json([
            'success' => true,
            'message' => 'Follower removed successfully'
        ]);
    }

    public function followStatus(Request $request, $username)
    {
        $user = User::where('username', $username)->firstOrFail();
        $currentUser = $request->user();

        if ($user->id === $currentUser->id) {
            return response()->json([
                'success' => true,
                'data' => [
                    'is_following' => false,
                    'is_followed_by' => false,
                    'follow_status' => 'self'
                ]
            ]);
        }

        $following = Follow::where('follower_id', $currentUser->id)
            ->where('following_id', $user->id)
            ->first();

        $followedBy = Follow::where('follower_id', $user->id)
            ->where('following_id', $currentUser->id)
            ->where('status', 'accepted')
            ->exists();

        $followStatus = 'not_following';
        $isFollowing = false;

        if ($following) {
            $followStatus = $following->status;
            $isFollowing = $following->status === 'accepted';
        }

        return response()->json([
            'success' => true,
            'data' => [
                'is_following' => $isFollowing,
                'is_followed_by' => $followedBy,
                'follow_status' => $followStatus,
                'follow_id' => $following?->id
            ]
        ]);
    }
}
