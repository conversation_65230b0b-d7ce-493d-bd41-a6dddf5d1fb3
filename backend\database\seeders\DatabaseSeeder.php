<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Call the TestUserSeeder
        $this->call(TestUserSeeder::class);

        // Create additional sample users
        \App\Models\User::factory(10)->create();

        // Create Instagram sample data (posts, media, follows, likes, comments)
        $this->call(InstagramDataSeeder::class);
    }
}
