<?php

namespace App\Http\Controllers;

use App\Models\Comment;
use App\Models\Post;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Http;

class CommentController extends Controller
{
    public function index($postId)
    {
        $post = Post::findOrFail($postId);

        $comments = $post->comments()
            ->with(['user', 'replies.user'])
            ->withCount(['likes', 'replies'])
            ->paginate(20);

        // Add user interaction data if authenticated
        if (auth()->check()) {
            $user = auth()->user();
            $comments->getCollection()->transform(function ($comment) use ($user) {
                $comment->is_liked = $comment->isLikedBy($user);

                // Also check replies
                $comment->replies->transform(function ($reply) use ($user) {
                    $reply->is_liked = $reply->isLikedBy($user);
                    return $reply;
                });

                return $comment;
            });
        }

        return response()->json([
            'success' => true,
            'data' => $comments
        ]);
    }

    public function store(Request $request, $postId)
    {
        $post = Post::findOrFail($postId);

        if ($post->comments_disabled) {
            return response()->json([
                'success' => false,
                'message' => 'Comments are disabled for this post'
            ], 400);
        }

        $validator = Validator::make($request->all(), [
            'content' => 'required|string|max:500',
            'parent_id' => 'nullable|exists:comments,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // If replying to a comment, make sure it belongs to this post
        if ($request->parent_id) {
            $parentComment = Comment::findOrFail($request->parent_id);
            if ($parentComment->post_id !== $post->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid parent comment'
                ], 400);
            }
        }

        $comment = Comment::create([
            'user_id' => $request->user()->id,
            'post_id' => $post->id,
            'parent_id' => $request->parent_id,
            'content' => $request->content,
        ]);

        $comment->load(['user', 'replies.user']);

        // Create notification
        NotificationService::createCommentNotification($request->user(), $comment, $post);

        // Increment post comments count
        $post->increment('comments_count');

        // Emit socket event for realtime updates
        $this->emitSocketEvent('post_comment_added', [
            'post_id' => $post->id,
            'comment' => $comment->toArray(),
            'user' => $request->user()->only(['id', 'username', 'name', 'avatar']),
            'timestamp' => now()->toISOString()
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Comment created successfully',
            'data' => $comment
        ], 201);
    }

    public function show($id)
    {
        $comment = Comment::with(['user', 'post', 'parent', 'replies.user'])
            ->withCount(['likes', 'replies'])
            ->findOrFail($id);

        // Add user interaction data if authenticated
        if (auth()->check()) {
            $comment->is_liked = $comment->isLikedBy(auth()->user());
        }

        return response()->json([
            'success' => true,
            'data' => $comment
        ]);
    }

    public function update(Request $request, $id)
    {
        $comment = Comment::findOrFail($id);

        // Check if user owns the comment
        if ($comment->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'content' => 'required|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $comment->update([
            'content' => $request->content,
        ]);

        $comment->load(['user', 'replies.user']);

        return response()->json([
            'success' => true,
            'message' => 'Comment updated successfully',
            'data' => $comment
        ]);
    }

    public function destroy(Request $request, $id)
    {
        $comment = Comment::findOrFail($id);

        // Check if user owns the comment or owns the post
        if (
            $comment->user_id !== $request->user()->id &&
            $comment->post->user_id !== $request->user()->id
        ) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        $comment->delete();

        return response()->json([
            'success' => true,
            'message' => 'Comment deleted successfully'
        ]);
    }

    public function like(Request $request, $id)
    {
        $comment = Comment::findOrFail($id);

        $isLiked = $comment->toggleLike($request->user());

        return response()->json([
            'success' => true,
            'message' => $isLiked ? 'Comment liked' : 'Comment unliked',
            'data' => [
                'is_liked' => $isLiked,
                'likes_count' => $comment->fresh()->likes_count
            ]
        ]);
    }

    public function replies($id)
    {
        $comment = Comment::findOrFail($id);

        $replies = $comment->replies()
            ->with(['user'])
            ->withCount(['likes'])
            ->paginate(10);

        // Add user interaction data if authenticated
        if (auth()->check()) {
            $user = auth()->user();
            $replies->getCollection()->transform(function ($reply) use ($user) {
                $reply->is_liked = $reply->isLikedBy($user);
                return $reply;
            });
        }

        return response()->json([
            'success' => true,
            'data' => $replies
        ]);
    }

    /**
     * Emit socket event to Socket.IO server
     */
    private function emitSocketEvent($event, $data)
    {
        try {
            $socketUrl = env('SOCKET_SERVER_URL', 'http://localhost:3001');
            Http::timeout(5)->post($socketUrl . '/api/emit', [
                'event' => $event,
                'data' => $data
            ]);
        } catch (\Exception $e) {
            // Log error but don't fail the request
            \Log::warning('Failed to emit socket event: ' . $e->getMessage());
        }
    }
}
